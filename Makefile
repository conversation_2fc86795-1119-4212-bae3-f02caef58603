# Live TV Player - Go版本

.PHONY: run build clean install help

# 默认目标
help:
	@echo "Live TV Player - Go本地版"
	@echo ""
	@echo "可用命令:"
	@echo "  make run     - 运行开发服务器"
	@echo "  make build   - 编译可执行文件"
	@echo "  make install - 安装依赖"
	@echo "  make clean   - 清理编译文件"
	@echo "  make cross   - 交叉编译多平台版本"

# 运行开发服务器
run:
	@echo "🚀 启动Live TV Player..."
	go run main.go

# 编译当前平台版本
build:
	@echo "📦 编译可执行文件..."
	go build -o livetv main.go
	@echo "✅ 编译完成: ./livetv"

# 安装依赖
install:
	@echo "📥 安装依赖..."
	go mod tidy
	@echo "✅ 依赖安装完成"

# 清理编译文件
clean:
	@echo "🧹 清理编译文件..."
	rm -f livetv livetv.exe livetv-*
	@echo "✅ 清理完成"

# 交叉编译多平台版本
cross:
	@echo "🔨 交叉编译多平台版本..."
	GOOS=windows GOARCH=amd64 go build -o livetv-windows-amd64.exe main.go
	GOOS=linux GOARCH=amd64 go build -o livetv-linux-amd64 main.go
	GOOS=darwin GOARCH=amd64 go build -o livetv-macos-amd64 main.go
	GOOS=linux GOARCH=arm64 go build -o livetv-linux-arm64 main.go
	GOOS=darwin GOARCH=arm64 go build -o livetv-macos-arm64 main.go
	@echo "✅ 交叉编译完成"
	@ls -la livetv-*
