# Live TV Player - Cloudflare Worker

一个基于Cloudflare Worker的直播电视播放器，支持播放M3U格式的直播列表。

## 功能特性

- 🔴 **实时直播播放** - 支持HLS/M3U8流媒体格式
- 📺 **频道列表** - 自动解析M3U文件获取频道信息
- 🎨 **现代化界面** - 响应式设计，支持移动端
- ⚡ **快速加载** - 基于Cloudflare边缘网络
- 🔄 **自动刷新** - 支持频道列表刷新
- 🎯 **智能解析** - 自动提取频道名称、分组、Logo等信息

## 默认数据源

默认使用 `https://tv-1.iill.top/m3u/Gather` 作为直播源

## 部署方法

### 1. 安装Wrangler CLI

```bash
npm install -g wrangler
```

### 2. 登录Cloudflare

```bash
wrangler login
```

### 3. 部署Worker

```bash
# 部署到开发环境
wrangler deploy --env dev

# 部署到生产环境  
wrangler deploy --env production

# 或直接部署
wrangler deploy
```

### 4. 访问应用

部署成功后，你会得到一个类似 `https://livetv-player.your-subdomain.workers.dev` 的URL。

## 本地开发

```bash
# 启动本地开发服务器
wrangler dev

# 指定端口
wrangler dev --port 8080
```

## API端点

- `GET /` - 主页面（HTML界面）
- `GET /api/channels` - 获取频道列表（JSON格式）

## 自定义配置

### 修改直播源

编辑 `worker.js` 文件中的 `M3U_URL` 常量：

```javascript
const M3U_URL = 'your-m3u-url-here';
```

### 添加自定义域名

在 `wrangler.toml` 中取消注释并修改routes配置：

```toml
routes = [
  { pattern = "your-domain.com/*", zone_name = "your-domain.com" }
]
```

## 支持的流媒体格式

- HLS (HTTP Live Streaming) - .m3u8
- MPEG-DASH
- 直接视频流 - .mp4, .webm等

## 浏览器兼容性

- Chrome/Edge 85+
- Firefox 78+
- Safari 14+
- 移动端浏览器

## 使用说明

1. 打开部署后的URL
2. 从下拉列表中选择要观看的频道
3. 点击"播放"按钮开始观看
4. 使用视频控件调节音量、全屏等

## 快捷键

- `Enter` - 在频道选择框中按回车直接播放
- 双击频道列表 - 直接播放选中频道

## 故障排除

### 视频无法播放
- 检查网络连接
- 尝试其他频道
- 确认浏览器支持该视频格式
- 检查CORS设置

### 频道列表为空
- 检查M3U源是否可访问
- 确认M3U文件格式正确
- 查看浏览器控制台错误信息

## 技术栈

- **后端**: Cloudflare Workers
- **前端**: 原生HTML/CSS/JavaScript
- **视频播放**: HTML5 Video API
- **部署**: Wrangler CLI

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
