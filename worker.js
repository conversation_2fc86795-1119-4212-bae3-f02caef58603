// Cloudflare Worker for Live TV Streaming
// 支持播放 https://tv-1.iill.top/m3u/Gather 的直播列表

const M3U_URL = 'https://tv-1.iill.top/m3u/Gather';

// 解析M3U文件
function parseM3U(content) {
  console.log('开始解析M3U，内容长度:', content.length);
  const lines = content.split(/\r?\n/);
  const channels = [];
  let currentChannel = {};
  let processedLines = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    if (line.startsWith('#EXTINF:')) {
      processedLines++;
      // 解析频道信息 - 更宽松的匹配
      const match = line.match(/#EXTINF:\s*(-?\d+(?:\.\d+)?)(?:\s*,\s*(.*))?/);
      if (match) {
        let titlePart = match[2] || '';

        // 提取各种属性
        const groupMatch = line.match(/group-title="([^"]+)"/i);
        const logoMatch = line.match(/tvg-logo="([^"]+)"/i);
        const idMatch = line.match(/tvg-id="([^"]+)"/i);
        const nameMatch = line.match(/tvg-name="([^"]+)"/i);

        // 从titlePart中提取实际标题（去掉最后的逗号后的内容）
        let cleanTitle = titlePart;
        if (titlePart.includes(',')) {
          const parts = titlePart.split(',');
          cleanTitle = parts[parts.length - 1].trim();
        }

        currentChannel = {
          duration: parseFloat(match[1]) || -1,
          title: cleanTitle || nameMatch?.[1] || idMatch?.[1] || '未知频道',
          originalTitle: titlePart
        };

        if (groupMatch) currentChannel.group = groupMatch[1];
        if (logoMatch) currentChannel.logo = logoMatch[1];
        if (idMatch) currentChannel.id = idMatch[1];
        if (nameMatch) currentChannel.name = nameMatch[1];

        // 确保标题不为空
        if (!currentChannel.title || currentChannel.title.length < 1) {
          currentChannel.title = currentChannel.name || currentChannel.id || `频道${processedLines}`;
        }
      }
    } else if (line && !line.startsWith('#') && currentChannel && currentChannel.title) {
      // 这是流媒体URL
      if (line.startsWith('http://') || line.startsWith('https://')) {
        currentChannel.url = line;
        channels.push({ ...currentChannel });
        console.log('添加频道:', currentChannel.title, currentChannel.url.substring(0, 50) + '...');
        currentChannel = {};
      }
    }
  }

  console.log('解析完成，处理了', processedLines, '个EXTINF行，得到', channels.length, '个频道');

  // 过滤掉无效的频道
  const validChannels = channels.filter(channel =>
    channel.title &&
    channel.url &&
    channel.title.length > 0 &&
    !channel.title.includes('INSERT_HERE') &&
    !channel.title.includes('MPD')
  );

  console.log('过滤后剩余', validChannels.length, '个有效频道');
  return validChannels;
}

// 生成HTML前端页面
function generateHTML(channels) {
  const channelOptions = channels.map((channel, index) => 
    `<option value="${index}" data-url="${channel.url}" data-logo="${channel.logo || ''}">${channel.title} ${channel.group ? `(${channel.group})` : ''}</option>`
  ).join('');

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live TV Player</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        select {
            flex: 1;
            min-width: 300px;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            background: white;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        
        select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        button {
            padding: 12px 25px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .player-container {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        video {
            width: 100%;
            height: auto;
            min-height: 400px;
            display: block;
        }
        
        .channel-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .channel-logo {
            width: 60px;
            height: 60px;
            object-fit: contain;
            border-radius: 8px;
            background: white;
            padding: 5px;
        }
        
        .channel-details h3 {
            color: #333;
            margin-bottom: 5px;
            font-size: 1.2em;
        }
        
        .channel-details p {
            color: #666;
            font-size: 0.9em;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
            font-size: 1.2em;
        }
        
        .error {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #d63031;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .controls {
                flex-direction: column;
            }
            
            select {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔴 Live TV Player</h1>
        
        <div class="controls">
            <select id="channelSelect">
                <option value="">选择频道...</option>
                ${channelOptions}
            </select>
            <button onclick="playChannel()">播放</button>
            <button onclick="refreshChannels()">刷新列表</button>
        </div>
        
        <div id="channelInfo" class="channel-info" style="display: none;">
            <img id="channelLogo" class="channel-logo" src="" alt="频道Logo" style="display: none;">
            <div class="channel-details">
                <h3 id="channelTitle"></h3>
                <p id="channelGroup"></p>
            </div>
        </div>
        
        <div class="player-container">
            <video id="videoPlayer" controls preload="none">
                您的浏览器不支持视频播放。
            </video>
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            正在加载频道...
        </div>
        
        <div id="error" class="error" style="display: none;"></div>

        <div id="debug" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 12px; color: #666; display: none;">
            <strong>调试信息:</strong><br>
            <div id="debugContent"></div>
        </div>
    </div>

    <script>
        const videoPlayer = document.getElementById('videoPlayer');
        const channelSelect = document.getElementById('channelSelect');
        const channelInfo = document.getElementById('channelInfo');
        const channelLogo = document.getElementById('channelLogo');
        const channelTitle = document.getElementById('channelTitle');
        const channelGroup = document.getElementById('channelGroup');
        const loading = document.getElementById('loading');
        const error = document.getElementById('error');
        const debug = document.getElementById('debug');
        const debugContent = document.getElementById('debugContent');
        
        function showError(message) {
            error.textContent = message;
            error.style.display = 'block';
            loading.style.display = 'none';
        }
        
        function hideError() {
            error.style.display = 'none';
        }
        
        function showLoading() {
            loading.style.display = 'block';
            hideError();
        }
        
        function hideLoading() {
            loading.style.display = 'none';
        }

        function showDebug(message) {
            debugContent.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            debug.style.display = 'block';
        }

        function hideDebug() {
            debug.style.display = 'none';
        }
        
        function playChannel() {
            const selectedOption = channelSelect.options[channelSelect.selectedIndex];
            if (!selectedOption || !selectedOption.value) {
                showError('请先选择一个频道');
                return;
            }
            
            const url = selectedOption.dataset.url;
            const logo = selectedOption.dataset.logo;
            const title = selectedOption.textContent;
            
            if (!url) {
                showError('频道URL无效');
                return;
            }
            
            showLoading();
            hideError();
            
            // 更新频道信息
            channelTitle.textContent = title;
            if (logo) {
                channelLogo.src = logo;
                channelLogo.style.display = 'block';
                channelLogo.onerror = () => channelLogo.style.display = 'none';
            } else {
                channelLogo.style.display = 'none';
            }
            channelInfo.style.display = 'flex';
            
            // 播放视频
            videoPlayer.src = url;
            videoPlayer.load();
            
            videoPlayer.onloadstart = () => showLoading();
            videoPlayer.oncanplay = () => {
                hideLoading();
                videoPlayer.play().catch(e => {
                    console.error('播放失败:', e);
                    showError('播放失败，请检查网络连接或尝试其他频道');
                });
            };
            videoPlayer.onerror = () => {
                hideLoading();
                showError('视频加载失败，请尝试其他频道');
            };
        }
        
        function refreshChannels() {
            showLoading();
            hideError();

            // 重新获取频道列表
            fetch('/api/channels')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                    }
                    return response.json();
                })
                .then(channels => {
                    hideLoading();
                    if (channels && channels.length > 0) {
                        // 重新填充频道列表
                        const channelSelect = document.getElementById('channelSelect');
                        channelSelect.innerHTML = '<option value="">选择频道...</option>';

                        channels.forEach((channel, index) => {
                            const option = document.createElement('option');
                            option.value = index;
                            option.dataset.url = channel.url;
                            option.dataset.logo = channel.logo || '';
                            option.textContent = channel.title + (channel.group ? ' (' + channel.group + ')' : '');
                            channelSelect.appendChild(option);
                        });

                        showError('✅ 频道列表已更新！共 ' + channels.length + ' 个频道');
                        setTimeout(hideError, 3000);
                    } else {
                        showError('❌ 未找到任何频道');
                    }
                })
                .catch(error => {
                    hideLoading();
                    console.error('刷新失败:', error);
                    showError('❌ 刷新失败: ' + error.message);
                });
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && channelSelect === document.activeElement) {
                playChannel();
            }
        });
        
        // 双击频道列表直接播放
        channelSelect.addEventListener('dblclick', playChannel);

        // 页面加载时显示调试信息
        window.addEventListener('load', function() {
            showDebug('页面加载完成');
            showDebug('频道数量: ' + (channelSelect.options.length - 1));

            // 测试API连接
            fetch('/api/channels')
                .then(response => {
                    showDebug('API响应状态: ' + response.status);
                    return response.json();
                })
                .then(channels => {
                    showDebug('API返回频道数: ' + channels.length);
                    if (channels.length === 0) {
                        showError('⚠️ API返回了空的频道列表，请检查M3U源是否可访问');
                    }
                })
                .catch(error => {
                    showDebug('API错误: ' + error.message);
                    showError('❌ 无法连接到API: ' + error.message);
                });
        });
    </script>
</body>
</html>`;
}

// 主处理函数
export default {
  async fetch(request) {
    const url = new URL(request.url);

    try {
      if (url.pathname === '/api/channels') {
        // API端点：获取频道列表
        const response = await fetch(M3U_URL, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/plain, application/vnd.apple.mpegurl, application/x-mpegurl, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://tv-1.iill.top/',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch M3U: ${response.status} ${response.statusText}`);
        }

        const m3uContent = await response.text();
        console.log('M3U content length:', m3uContent.length);
        console.log('M3U content preview:', m3uContent.substring(0, 500));

        const channels = parseM3U(m3uContent);
        console.log('Parsed channels count:', channels.length);

        if (channels.length > 0) {
          console.log('First channel example:', JSON.stringify(channels[0], null, 2));
        }

        return new Response(JSON.stringify(channels), {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Cache-Control': 'public, max-age=300' // 缓存5分钟
          }
        });
      } else {
        // 默认返回HTML页面
        const response = await fetch(M3U_URL, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch M3U: ${response.status} ${response.statusText}`);
        }

        const m3uContent = await response.text();
        const channels = parseM3U(m3uContent);
        const html = generateHTML(channels);

        return new Response(html, {
          headers: {
            'Content-Type': 'text/html; charset=utf-8',
            'Cache-Control': 'public, max-age=300' // 缓存5分钟
          }
        });
      }
    } catch (error) {
      console.error('Error:', error);

      const errorHtml = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>错误 - Live TV Player</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    padding: 50px;
                    text-align: center;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .error {
                    color: #d63031;
                    background: rgba(255, 255, 255, 0.95);
                    padding: 40px;
                    border-radius: 15px;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                    max-width: 500px;
                }
                .error h2 {
                    margin-bottom: 20px;
                    color: #333;
                }
                .error p {
                    margin-bottom: 30px;
                    line-height: 1.6;
                }
                button {
                    padding: 12px 25px;
                    background: linear-gradient(45deg, #667eea, #764ba2);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 16px;
                    font-weight: bold;
                    transition: transform 0.2s;
                }
                button:hover {
                    transform: translateY(-2px);
                }
                .debug-info {
                    margin-top: 20px;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    font-size: 14px;
                    color: #666;
                    text-align: left;
                }
            </style>
        </head>
        <body>
            <div class="error">
                <h2>🚫 加载失败</h2>
                <p>无法获取直播列表，请稍后重试</p>
                <div class="debug-info">
                    <strong>错误详情:</strong><br>
                    ${error.message}<br><br>
                    <strong>M3U源:</strong><br>
                    ${M3U_URL}
                </div>
                <button onclick="location.reload()">🔄 重试</button>
            </div>
        </body>
        </html>
      `;

      return new Response(errorHtml, {
        status: 500,
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      });
    }
  }
};
