// Cloudflare Worker for Live TV Streaming
// 支持播放 https://tv-1.iill.top/m3u/Gather 的直播列表

// 多个备用M3U源
const M3U_URLS = [
  'https://tv-1.iill.top/m3u/Gather',
  'https://raw.githubusercontent.com/fanmingming/live/main/tv/m3u/ipv6.m3u8',
  'https://raw.githubusercontent.com/YueChan/Live/main/IPTV.m3u8',
  'https://raw.githubusercontent.com/Kimentanm/aptv/master/m3u/iptv.m3u'
];

// 尝试获取M3U内容的函数
async function fetchM3UContent() {
  const methods = [
    // 方法1: 模拟浏览器完整请求
    (url) => fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0'
      }
    }),
    // 方法2: 简单请求
    (url) => fetch(url, {
      headers: {
        'User-Agent': 'VLC/3.0.16 LibVLC/3.0.16',
        'Accept': '*/*'
      }
    }),
    // 方法3: 无头请求
    (url) => fetch(url)
  ];

  for (const url of M3U_URLS) {
    console.log(`尝试M3U源: ${url}`);

    for (let i = 0; i < methods.length; i++) {
      try {
        console.log(`  方法${i + 1}: 发送请求...`);
        const response = await methods[i](url);
        console.log(`  方法${i + 1}: 状态 ${response.status}`);

        if (response.ok) {
          const content = await response.text();
          console.log(`  方法${i + 1}: 内容长度 ${content.length}`);
          console.log(`  方法${i + 1}: 前100字符: ${content.substring(0, 100)}`);

          // 检查是否是有效的M3U内容
          if (content.includes('#EXTM3U') || content.includes('#EXTINF')) {
            console.log(`✅ 成功获取M3U内容！源: ${url}, 方法: ${i + 1}`);
            return { content, source: url, method: i + 1 };
          } else if (content.includes('<!DOCTYPE html>')) {
            console.log(`  方法${i + 1}: 返回HTML页面，尝试下一个方法`);
            continue;
          } else {
            console.log(`  方法${i + 1}: 内容格式不正确`);
          }
        }
      } catch (error) {
        console.log(`  方法${i + 1}: 失败 - ${error.message}`);
      }
    }
  }

  throw new Error('所有M3U源和方法都失败了');
}

// 改进的M3U解析函数 - 基于提供的更完善的解析逻辑
function parseM3U(m3uContent) {
  console.log('开始解析M3U，内容长度:', m3uContent.length);

  const channels = [];
  const lines = m3uContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);

  let tvgUrl = '';
  let channelIndex = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // 检查是否是 #EXTM3U 行，提取 tvg-url
    if (line.startsWith('#EXTM3U')) {
      // 支持两种格式：x-tvg-url 和 url-tvg
      const tvgUrlMatch = line.match(/(?:x-tvg-url|url-tvg)="([^"]*)"/);
      tvgUrl = tvgUrlMatch ? tvgUrlMatch[1].split(',')[0].trim() : '';
      console.log('发现TVG URL:', tvgUrl);
      continue;
    }

    // 检查是否是 #EXTINF 行
    if (line.startsWith('#EXTINF:')) {
      // 提取 tvg-id
      const tvgIdMatch = line.match(/tvg-id="([^"]*)"/);
      const tvgId = tvgIdMatch ? tvgIdMatch[1] : '';

      // 提取 tvg-name
      const tvgNameMatch = line.match(/tvg-name="([^"]*)"/);
      const tvgName = tvgNameMatch ? tvgNameMatch[1] : '';

      // 提取 tvg-logo
      const tvgLogoMatch = line.match(/tvg-logo="([^"]*)"/);
      const logo = tvgLogoMatch ? tvgLogoMatch[1] : '';

      // 提取 group-title
      const groupTitleMatch = line.match(/group-title="([^"]*)"/);
      const group = groupTitleMatch ? groupTitleMatch[1] : '无分组';

      // 提取标题（#EXTINF 行最后的逗号后面的内容）
      const titleMatch = line.match(/,([^,]*)$/);
      const title = titleMatch ? titleMatch[1].trim() : '';

      // 优先使用标题，如果没有则使用 tvg-name
      const name = title || tvgName || tvgId || `频道${channelIndex + 1}`;

      // 检查下一行是否是URL
      if (i + 1 < lines.length && !lines[i + 1].startsWith('#')) {
        const url = lines[i + 1];

        // 只有当有名称和URL时才添加到结果中
        if (name && url && (url.startsWith('http://') || url.startsWith('https://'))) {
          channels.push({
            id: `channel-${channelIndex}`,
            tvgId,
            title: name,
            name: tvgName,
            logo,
            group,
            url
          });

          console.log(`添加频道 ${channelIndex + 1}: ${name} (${group})`);
          channelIndex++;
        }

        // 跳过下一行，因为已经处理了
        i++;
      }
    }
  }

  // 过滤掉无效的频道
  const validChannels = channels.filter(channel =>
    channel.title &&
    channel.url &&
    channel.title.length > 0 &&
    !channel.title.includes('INSERT_HERE') &&
    !channel.title.includes('MPD') &&
    !channel.title.includes('免費訂閲') &&
    !channel.title.includes('維護')
  );

  console.log(`解析完成: 总共${channels.length}个频道，过滤后${validChannels.length}个有效频道`);

  return {
    tvgUrl,
    channels: validChannels
  };
}

// 生成HTML前端页面
function generateHTML(channels) {
  const channelOptions = channels.map((channel, index) => 
    `<option value="${index}" data-url="${channel.url}" data-logo="${channel.logo || ''}">${channel.title} ${channel.group ? `(${channel.group})` : ''}</option>`
  ).join('');

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live TV Player</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        select {
            flex: 1;
            min-width: 300px;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            background: white;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        
        select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        button {
            padding: 12px 25px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .player-container {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        video {
            width: 100%;
            height: auto;
            min-height: 400px;
            display: block;
        }
        
        .channel-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .channel-logo {
            width: 60px;
            height: 60px;
            object-fit: contain;
            border-radius: 8px;
            background: white;
            padding: 5px;
        }
        
        .channel-details h3 {
            color: #333;
            margin-bottom: 5px;
            font-size: 1.2em;
        }
        
        .channel-details p {
            color: #666;
            font-size: 0.9em;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
            font-size: 1.2em;
        }
        
        .error {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #d63031;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .controls {
                flex-direction: column;
            }
            
            select {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔴 Live TV Player</h1>
        
        <div class="controls">
            <select id="channelSelect">
                <option value="">选择频道...</option>
                ${channelOptions}
            </select>
            <button onclick="playChannel()">播放</button>
            <button onclick="refreshChannels()">刷新列表</button>
        </div>
        
        <div id="channelInfo" class="channel-info" style="display: none;">
            <img id="channelLogo" class="channel-logo" src="" alt="频道Logo" style="display: none;">
            <div class="channel-details">
                <h3 id="channelTitle"></h3>
                <p id="channelGroup"></p>
            </div>
        </div>
        
        <div class="player-container">
            <video id="videoPlayer" controls preload="none">
                您的浏览器不支持视频播放。
            </video>
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            正在加载频道...
        </div>
        
        <div id="error" class="error" style="display: none;"></div>

        <div id="debug" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 12px; color: #666; display: none;">
            <strong>调试信息:</strong><br>
            <div id="debugContent"></div>
        </div>
    </div>

    <script>
        const videoPlayer = document.getElementById('videoPlayer');
        const channelSelect = document.getElementById('channelSelect');
        const channelInfo = document.getElementById('channelInfo');
        const channelLogo = document.getElementById('channelLogo');
        const channelTitle = document.getElementById('channelTitle');
        const channelGroup = document.getElementById('channelGroup');
        const loading = document.getElementById('loading');
        const error = document.getElementById('error');
        const debug = document.getElementById('debug');
        const debugContent = document.getElementById('debugContent');
        
        function showError(message) {
            error.textContent = message;
            error.style.display = 'block';
            loading.style.display = 'none';
        }
        
        function hideError() {
            error.style.display = 'none';
        }
        
        function showLoading() {
            loading.style.display = 'block';
            hideError();
        }
        
        function hideLoading() {
            loading.style.display = 'none';
        }

        function showDebug(message) {
            debugContent.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            debug.style.display = 'block';
        }

        function hideDebug() {
            debug.style.display = 'none';
        }
        
        function playChannel() {
            const selectedOption = channelSelect.options[channelSelect.selectedIndex];
            if (!selectedOption || !selectedOption.value) {
                showError('请先选择一个频道');
                return;
            }
            
            const url = selectedOption.dataset.url;
            const logo = selectedOption.dataset.logo;
            const title = selectedOption.textContent;
            
            if (!url) {
                showError('频道URL无效');
                return;
            }
            
            showLoading();
            hideError();
            
            // 更新频道信息
            channelTitle.textContent = title;
            if (logo) {
                channelLogo.src = logo;
                channelLogo.style.display = 'block';
                channelLogo.onerror = () => channelLogo.style.display = 'none';
            } else {
                channelLogo.style.display = 'none';
            }
            channelInfo.style.display = 'flex';
            
            // 播放视频
            videoPlayer.src = url;
            videoPlayer.load();
            
            videoPlayer.onloadstart = () => showLoading();
            videoPlayer.oncanplay = () => {
                hideLoading();
                videoPlayer.play().catch(e => {
                    console.error('播放失败:', e);
                    showError('播放失败，请检查网络连接或尝试其他频道');
                });
            };
            videoPlayer.onerror = () => {
                hideLoading();
                showError('视频加载失败，请尝试其他频道');
            };
        }
        
        function refreshChannels() {
            showLoading();
            hideError();

            // 重新获取频道列表
            fetch('/api/channels')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                    }
                    return response.json();
                })
                .then(channels => {
                    hideLoading();
                    if (channels && channels.length > 0) {
                        // 重新填充频道列表
                        const channelSelect = document.getElementById('channelSelect');
                        channelSelect.innerHTML = '<option value="">选择频道...</option>';

                        channels.forEach((channel, index) => {
                            const option = document.createElement('option');
                            option.value = index;
                            option.dataset.url = channel.url;
                            option.dataset.logo = channel.logo || '';
                            option.textContent = channel.title + (channel.group ? ' (' + channel.group + ')' : '');
                            channelSelect.appendChild(option);
                        });

                        showError('✅ 频道列表已更新！共 ' + channels.length + ' 个频道');
                        setTimeout(hideError, 3000);
                    } else {
                        showError('❌ 未找到任何频道');
                    }
                })
                .catch(error => {
                    hideLoading();
                    console.error('刷新失败:', error);
                    showError('❌ 刷新失败: ' + error.message);
                });
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && channelSelect === document.activeElement) {
                playChannel();
            }
        });
        
        // 双击频道列表直接播放
        channelSelect.addEventListener('dblclick', playChannel);

        // 页面加载时显示调试信息
        window.addEventListener('load', function() {
            showDebug('页面加载完成');
            showDebug('频道数量: ' + (channelSelect.options.length - 1));

            // 测试API连接
            fetch('/api/channels')
                .then(response => {
                    showDebug('API响应状态: ' + response.status);
                    return response.json();
                })
                .then(channels => {
                    showDebug('API返回频道数: ' + channels.length);
                    if (channels.length === 0) {
                        showError('⚠️ API返回了空的频道列表，请检查M3U源是否可访问');
                    }
                })
                .catch(error => {
                    showDebug('API错误: ' + error.message);
                    showError('❌ 无法连接到API: ' + error.message);
                });
        });
    </script>
</body>
</html>`;
}

// 主处理函数
export default {
  async fetch(request) {
    const url = new URL(request.url);

    try {
      if (url.pathname === '/api/channels') {
        // API端点：获取频道列表
        const result = await fetchM3UContent();
        const parseResult = parseM3U(result.content);
        const channels = parseResult.channels;

        console.log('M3U获取成功:', result.source, '方法:', result.method);
        console.log('TVG URL:', parseResult.tvgUrl);
        console.log('解析到频道数:', channels.length);

        if (channels.length > 0) {
          console.log('第一个频道示例:', JSON.stringify(channels[0], null, 2));
        }

        return new Response(JSON.stringify(channels), {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Cache-Control': 'public, max-age=300' // 缓存5分钟
          }
        });
      } else {
        // 默认返回HTML页面
        const result = await fetchM3UContent();
        const parseResult = parseM3U(result.content);
        const channels = parseResult.channels;
        const html = generateHTML(channels);

        return new Response(html, {
          headers: {
            'Content-Type': 'text/html; charset=utf-8',
            'Cache-Control': 'public, max-age=300' // 缓存5分钟
          }
        });
      }
    } catch (error) {
      console.error('Error:', error);

      const errorHtml = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>错误 - Live TV Player</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    padding: 50px;
                    text-align: center;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .error {
                    color: #d63031;
                    background: rgba(255, 255, 255, 0.95);
                    padding: 40px;
                    border-radius: 15px;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                    max-width: 500px;
                }
                .error h2 {
                    margin-bottom: 20px;
                    color: #333;
                }
                .error p {
                    margin-bottom: 30px;
                    line-height: 1.6;
                }
                button {
                    padding: 12px 25px;
                    background: linear-gradient(45deg, #667eea, #764ba2);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 16px;
                    font-weight: bold;
                    transition: transform 0.2s;
                }
                button:hover {
                    transform: translateY(-2px);
                }
                .debug-info {
                    margin-top: 20px;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    font-size: 14px;
                    color: #666;
                    text-align: left;
                }
            </style>
        </head>
        <body>
            <div class="error">
                <h2>🚫 加载失败</h2>
                <p>无法获取直播列表，请稍后重试</p>
                <div class="debug-info">
                    <strong>错误详情:</strong><br>
                    ${error.message}<br><br>
                    <strong>M3U源:</strong><br>
                    ${M3U_URLS.join('<br>')}
                </div>
                <button onclick="location.reload()">🔄 重试</button>
            </div>
        </body>
        </html>
      `;

      return new Response(errorHtml, {
        status: 500,
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      });
    }
  }
};
