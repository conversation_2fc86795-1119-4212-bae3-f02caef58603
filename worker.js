// Cloudflare Worker for Live TV Streaming
// 支持播放 https://tv-1.iill.top/m3u/Gather 的直播列表

const M3U_URL = 'https://tv-1.iill.top/m3u/Gather';

// 解析M3U文件
function parseM3U(content) {
  const lines = content.split('\n');
  const channels = [];
  let currentChannel = {};
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    if (line.startsWith('#EXTINF:')) {
      // 解析频道信息
      const match = line.match(/#EXTINF:(-?\d+(?:\.\d+)?),(.+)/);
      if (match) {
        currentChannel = {
          duration: parseFloat(match[1]),
          title: match[2].trim()
        };
        
        // 提取group-title和tvg-logo等属性
        const groupMatch = line.match(/group-title="([^"]+)"/);
        const logoMatch = line.match(/tvg-logo="([^"]+)"/);
        const idMatch = line.match(/tvg-id="([^"]+)"/);
        
        if (groupMatch) currentChannel.group = groupMatch[1];
        if (logoMatch) currentChannel.logo = logoMatch[1];
        if (idMatch) currentChannel.id = idMatch[1];
      }
    } else if (line && !line.startsWith('#') && currentChannel.title) {
      // 这是流媒体URL
      currentChannel.url = line;
      channels.push({ ...currentChannel });
      currentChannel = {};
    }
  }
  
  return channels;
}

// 生成HTML前端页面
function generateHTML(channels) {
  const channelOptions = channels.map((channel, index) => 
    `<option value="${index}" data-url="${channel.url}" data-logo="${channel.logo || ''}">${channel.title} ${channel.group ? `(${channel.group})` : ''}</option>`
  ).join('');

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live TV Player</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        select {
            flex: 1;
            min-width: 300px;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            background: white;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        
        select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        button {
            padding: 12px 25px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .player-container {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        video {
            width: 100%;
            height: auto;
            min-height: 400px;
            display: block;
        }
        
        .channel-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .channel-logo {
            width: 60px;
            height: 60px;
            object-fit: contain;
            border-radius: 8px;
            background: white;
            padding: 5px;
        }
        
        .channel-details h3 {
            color: #333;
            margin-bottom: 5px;
            font-size: 1.2em;
        }
        
        .channel-details p {
            color: #666;
            font-size: 0.9em;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
            font-size: 1.2em;
        }
        
        .error {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #d63031;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .controls {
                flex-direction: column;
            }
            
            select {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔴 Live TV Player</h1>
        
        <div class="controls">
            <select id="channelSelect">
                <option value="">选择频道...</option>
                ${channelOptions}
            </select>
            <button onclick="playChannel()">播放</button>
            <button onclick="refreshChannels()">刷新列表</button>
        </div>
        
        <div id="channelInfo" class="channel-info" style="display: none;">
            <img id="channelLogo" class="channel-logo" src="" alt="频道Logo" style="display: none;">
            <div class="channel-details">
                <h3 id="channelTitle"></h3>
                <p id="channelGroup"></p>
            </div>
        </div>
        
        <div class="player-container">
            <video id="videoPlayer" controls preload="none">
                您的浏览器不支持视频播放。
            </video>
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            正在加载频道...
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        const videoPlayer = document.getElementById('videoPlayer');
        const channelSelect = document.getElementById('channelSelect');
        const channelInfo = document.getElementById('channelInfo');
        const channelLogo = document.getElementById('channelLogo');
        const channelTitle = document.getElementById('channelTitle');
        const channelGroup = document.getElementById('channelGroup');
        const loading = document.getElementById('loading');
        const error = document.getElementById('error');
        
        function showError(message) {
            error.textContent = message;
            error.style.display = 'block';
            loading.style.display = 'none';
        }
        
        function hideError() {
            error.style.display = 'none';
        }
        
        function showLoading() {
            loading.style.display = 'block';
            hideError();
        }
        
        function hideLoading() {
            loading.style.display = 'none';
        }
        
        function playChannel() {
            const selectedOption = channelSelect.options[channelSelect.selectedIndex];
            if (!selectedOption || !selectedOption.value) {
                showError('请先选择一个频道');
                return;
            }
            
            const url = selectedOption.dataset.url;
            const logo = selectedOption.dataset.logo;
            const title = selectedOption.textContent;
            
            if (!url) {
                showError('频道URL无效');
                return;
            }
            
            showLoading();
            hideError();
            
            // 更新频道信息
            channelTitle.textContent = title;
            if (logo) {
                channelLogo.src = logo;
                channelLogo.style.display = 'block';
                channelLogo.onerror = () => channelLogo.style.display = 'none';
            } else {
                channelLogo.style.display = 'none';
            }
            channelInfo.style.display = 'flex';
            
            // 播放视频
            videoPlayer.src = url;
            videoPlayer.load();
            
            videoPlayer.onloadstart = () => showLoading();
            videoPlayer.oncanplay = () => {
                hideLoading();
                videoPlayer.play().catch(e => {
                    console.error('播放失败:', e);
                    showError('播放失败，请检查网络连接或尝试其他频道');
                });
            };
            videoPlayer.onerror = () => {
                hideLoading();
                showError('视频加载失败，请尝试其他频道');
            };
        }
        
        function refreshChannels() {
            location.reload();
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && channelSelect === document.activeElement) {
                playChannel();
            }
        });
        
        // 双击频道列表直接播放
        channelSelect.addEventListener('dblclick', playChannel);
    </script>
</body>
</html>`;
}

// 主处理函数
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    
    try {
      if (url.pathname === '/api/channels') {
        // API端点：获取频道列表
        const response = await fetch(M3U_URL);
        if (!response.ok) {
          throw new Error(`Failed to fetch M3U: ${response.status}`);
        }
        
        const m3uContent = await response.text();
        const channels = parseM3U(m3uContent);
        
        return new Response(JSON.stringify(channels), {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Cache-Control': 'public, max-age=300' // 缓存5分钟
          }
        });
      } else {
        // 默认返回HTML页面
        const response = await fetch(M3U_URL);
        if (!response.ok) {
          throw new Error(`Failed to fetch M3U: ${response.status}`);
        }
        
        const m3uContent = await response.text();
        const channels = parseM3U(m3uContent);
        const html = generateHTML(channels);
        
        return new Response(html, {
          headers: {
            'Content-Type': 'text/html; charset=utf-8',
            'Cache-Control': 'public, max-age=300' // 缓存5分钟
          }
        });
      }
    } catch (error) {
      console.error('Error:', error);
      
      const errorHtml = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>错误</title>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; padding: 50px; text-align: center; }
                .error { color: #d63031; background: #ffe6e6; padding: 20px; border-radius: 8px; }
            </style>
        </head>
        <body>
            <div class="error">
                <h2>加载失败</h2>
                <p>无法获取直播列表: ${error.message}</p>
                <button onclick="location.reload()">重试</button>
            </div>
        </body>
        </html>
      `;
      
      return new Response(errorHtml, {
        status: 500,
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      });
    }
  }
};
