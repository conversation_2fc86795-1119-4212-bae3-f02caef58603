// 改进版本 - 使用更完善的M3U解析逻辑

// 多个备用M3U源
const M3U_URLS = [
  'https://raw.githubusercontent.com/fanmingming/live/main/tv/m3u/ipv6.m3u8',
  'https://raw.githubusercontent.com/YueChan/Live/main/IPTV.m3u8',
  'https://tv-1.iill.top/m3u/Gather',
  'https://raw.githubusercontent.com/Kimentanm/aptv/master/m3u/iptv.m3u'
];

// 改进的M3U解析函数
function parseM3U(m3uContent) {
  console.log('开始解析M3U，内容长度:', m3uContent.length);
  
  const channels = [];
  const lines = m3uContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);

  let tvgUrl = '';
  let channelIndex = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // 检查是否是 #EXTM3U 行，提取 tvg-url
    if (line.startsWith('#EXTM3U')) {
      const tvgUrlMatch = line.match(/(?:x-tvg-url|url-tvg)="([^"]*)"/);
      tvgUrl = tvgUrlMatch ? tvgUrlMatch[1].split(',')[0].trim() : '';
      continue;
    }

    // 检查是否是 #EXTINF 行
    if (line.startsWith('#EXTINF:')) {
      // 提取各种属性
      const tvgIdMatch = line.match(/tvg-id="([^"]*)"/);
      const tvgId = tvgIdMatch ? tvgIdMatch[1] : '';

      const tvgNameMatch = line.match(/tvg-name="([^"]*)"/);
      const tvgName = tvgNameMatch ? tvgNameMatch[1] : '';

      const tvgLogoMatch = line.match(/tvg-logo="([^"]*)"/);
      const logo = tvgLogoMatch ? tvgLogoMatch[1] : '';

      const groupTitleMatch = line.match(/group-title="([^"]*)"/);
      const group = groupTitleMatch ? groupTitleMatch[1] : '无分组';

      // 提取标题（#EXTINF 行最后的逗号后面的内容）
      const titleMatch = line.match(/,([^,]*)$/);
      const title = titleMatch ? titleMatch[1].trim() : '';

      // 优先使用标题，如果没有则使用 tvg-name
      const name = title || tvgName || tvgId || `频道${channelIndex + 1}`;

      // 检查下一行是否是URL
      if (i + 1 < lines.length && !lines[i + 1].startsWith('#')) {
        const url = lines[i + 1];

        // 只有当有名称和URL时才添加到结果中
        if (name && url && (url.startsWith('http://') || url.startsWith('https://'))) {
          channels.push({
            id: `channel-${channelIndex}`,
            tvgId,
            title: name,
            name: tvgName,
            logo,
            group,
            url
          });
          channelIndex++;
        }

        // 跳过下一行，因为已经处理了
        i++;
      }
    }
  }

  // 过滤掉无效的频道
  const validChannels = channels.filter(channel => 
    channel.title && 
    channel.url && 
    channel.title.length > 0 &&
    !channel.title.includes('INSERT_HERE') &&
    !channel.title.includes('MPD') &&
    !channel.title.includes('免費訂閲') &&
    !channel.title.includes('維護') &&
    !channel.title.includes('温馨提示')
  );

  console.log(`解析完成: 总共${channels.length}个频道，过滤后${validChannels.length}个有效频道`);
  
  return {
    tvgUrl,
    channels: validChannels
  };
}

// 尝试获取M3U内容
async function fetchM3UContent() {
  const methods = [
    // 方法1: 标准请求
    (url) => fetch(url),
    // 方法2: 模拟浏览器
    (url) => fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
      }
    })
  ];

  for (const url of M3U_URLS) {
    console.log(`尝试M3U源: ${url}`);
    
    for (let i = 0; i < methods.length; i++) {
      try {
        const response = await methods[i](url);
        
        if (response.ok) {
          const content = await response.text();
          console.log(`方法${i + 1}: 内容长度 ${content.length}`);
          
          // 检查是否是有效的M3U内容
          if (content.includes('#EXTM3U') || content.includes('#EXTINF')) {
            console.log(`✅ 成功获取M3U内容！源: ${url}`);
            return { content, source: url, method: i + 1 };
          }
        }
      } catch (error) {
        console.log(`方法${i + 1}: 失败 - ${error.message}`);
      }
    }
  }
  
  throw new Error('所有M3U源都无法访问');
}

export default {
  async fetch(request) {
    const url = new URL(request.url);
    
    try {
      if (url.pathname === '/api/channels') {
        const result = await fetchM3UContent();
        const parseResult = parseM3U(result.content);
        
        return new Response(JSON.stringify({
          channels: parseResult.channels,
          tvgUrl: parseResult.tvgUrl,
          source: result.source,
          method: result.method,
          count: parseResult.channels.length
        }), {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        });
      }
      
      // 默认页面
      const result = await fetchM3UContent();
      const parseResult = parseM3U(result.content);
      const channels = parseResult.channels;
      
      const channelOptions = channels.map((channel, index) => 
        `<option value="${index}" data-url="${channel.url}" data-logo="${channel.logo || ''}" data-group="${channel.group}">${channel.title} ${channel.group ? `(${channel.group})` : ''}</option>`
      ).join('');

      const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live TV Player - 改进版</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; margin: 0; }
        .container { max-width: 1200px; margin: 0 auto; background: rgba(255,255,255,0.95); padding: 30px; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }
        .info { background: linear-gradient(45deg, #e8f5e8, #f0f8f0); padding: 20px; border-radius: 10px; margin-bottom: 25px; border-left: 4px solid #28a745; }
        .info strong { color: #155724; }
        select { width: 100%; padding: 15px; margin: 15px 0; border: 2px solid #ddd; border-radius: 10px; font-size: 16px; background: white; }
        select:focus { outline: none; border-color: #667eea; }
        .controls { display: flex; gap: 15px; margin-bottom: 25px; flex-wrap: wrap; }
        button { padding: 15px 30px; background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; border-radius: 10px; cursor: pointer; font-size: 16px; font-weight: bold; transition: transform 0.2s; }
        button:hover { transform: translateY(-2px); }
        .channel-info { margin: 20px 0; padding: 20px; background: rgba(102, 126, 234, 0.1); border-radius: 10px; display: none; border-left: 4px solid #667eea; }
        .channel-info img { width: 60px; height: 60px; object-fit: contain; border-radius: 8px; margin-right: 15px; float: left; }
        video { width: 100%; max-width: 100%; margin-top: 20px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .error { background: #ffe6e6; color: #d63031; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #d63031; display: none; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #667eea; }
        .stat-label { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔴 Live TV Player (改进版)</h1>
        
        <div class="info">
            <strong>✅ 解析成功!</strong><br>
            <strong>M3U源:</strong> ${result.source}<br>
            <strong>TVG URL:</strong> ${parseResult.tvgUrl || '无'}<br>
            <strong>获取方法:</strong> 方法${result.method}
        </div>
        
        <div class="controls">
            <select id="channelSelect" style="flex: 1; min-width: 300px;">
                <option value="">选择频道 (共${channels.length}个)...</option>
                ${channelOptions}
            </select>
            <button onclick="playChannel()">▶️ 播放</button>
            <button onclick="location.reload()">🔄 刷新</button>
        </div>
        
        <div id="channelInfo" class="channel-info">
            <img id="channelLogo" src="" alt="频道Logo" style="display: none;">
            <div>
                <h3 id="channelTitle"></h3>
                <p id="channelGroup"></p>
                <p id="channelStatus"></p>
            </div>
        </div>
        
        <video id="player" controls style="display: none;"></video>
        
        <div id="error" class="error"></div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${channels.length}</div>
                <div class="stat-label">总频道数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${new Set(channels.map(c => c.group)).size}</div>
                <div class="stat-label">分组数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${channels.filter(c => c.logo).length}</div>
                <div class="stat-label">有Logo</div>
            </div>
        </div>
    </div>
    
    <script>
        function showError(msg) {
            const error = document.getElementById('error');
            error.textContent = msg;
            error.style.display = 'block';
        }
        
        function hideError() {
            document.getElementById('error').style.display = 'none';
        }
        
        function playChannel() {
            const select = document.getElementById('channelSelect');
            const player = document.getElementById('player');
            const channelInfo = document.getElementById('channelInfo');
            const channelTitle = document.getElementById('channelTitle');
            const channelGroup = document.getElementById('channelGroup');
            const channelStatus = document.getElementById('channelStatus');
            const channelLogo = document.getElementById('channelLogo');
            const option = select.options[select.selectedIndex];
            
            hideError();
            
            if (option && option.dataset.url) {
                // 显示频道信息
                channelTitle.textContent = option.textContent;
                channelGroup.textContent = '分组: ' + (option.dataset.group || '无分组');
                channelStatus.textContent = '正在连接...';
                channelInfo.style.display = 'block';
                
                // 显示Logo
                if (option.dataset.logo) {
                    channelLogo.src = option.dataset.logo;
                    channelLogo.style.display = 'block';
                    channelLogo.onerror = () => channelLogo.style.display = 'none';
                } else {
                    channelLogo.style.display = 'none';
                }
                
                // 播放视频
                player.src = option.dataset.url;
                player.style.display = 'block';
                
                player.onloadstart = () => channelStatus.textContent = '⏳ 正在加载...';
                player.oncanplay = () => channelStatus.textContent = '✅ 连接成功';
                player.onerror = () => {
                    channelStatus.textContent = '❌ 播放失败';
                    showError('播放失败，请尝试其他频道或检查网络连接');
                };
                
                player.play().catch(e => {
                    console.error('播放错误:', e);
                    channelStatus.textContent = '❌ 播放失败';
                    showError('播放失败: ' + e.message);
                });
            } else {
                showError('请先选择一个频道');
            }
        }
        
        // 双击选择框直接播放
        document.getElementById('channelSelect').addEventListener('dblclick', playChannel);
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && document.getElementById('channelSelect') === document.activeElement) {
                playChannel();
            }
        });
    </script>
</body>
</html>`;
      
      return new Response(html, {
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      });
      
    } catch (error) {
      console.error('错误:', error);
      return new Response(`
<!DOCTYPE html>
<html><head><title>错误</title><meta charset="UTF-8"></head>
<body style="font-family: Arial; padding: 50px; text-align: center; background: #f5f5f5;">
    <div style="background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <h2>❌ 加载失败</h2>
        <p>错误: ${error.message}</p>
        <button onclick="location.reload()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">重试</button>
    </div>
</body></html>`, {
        status: 500,
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      });
    }
  }
};
