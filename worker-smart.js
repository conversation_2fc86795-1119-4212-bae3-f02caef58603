// 智能版本 - 尝试多种方法获取M3U文件

const M3U_URL = 'https://tv-1.iill.top/m3u/Gather';

// 尝试多种方法获取M3U内容
async function fetchM3UContent() {
  const methods = [
    // 方法1: 直接访问
    {
      name: '直接访问',
      fetch: () => fetch(M3U_URL, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/plain, application/vnd.apple.mpegurl, application/x-mpegurl, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Referer': 'https://tv-1.iill.top/',
          'Cache-Control': 'no-cache'
        }
      })
    },
    // 方法2: 模拟浏览器访问
    {
      name: '模拟浏览器',
      fetch: () => fetch(M3U_URL, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': '*/*',
          'Accept-Encoding': 'gzip, deflate, br',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Connection': 'keep-alive',
          'Referer': 'https://tv-1.iill.top/',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin'
        }
      })
    },
    // 方法3: 简单请求
    {
      name: '简单请求',
      fetch: () => fetch(M3U_URL)
    }
  ];

  for (const method of methods) {
    try {
      console.log(`尝试${method.name}...`);
      const response = await method.fetch();
      console.log(`${method.name} - 状态:`, response.status);
      
      if (response.ok) {
        const content = await response.text();
        console.log(`${method.name} - 内容长度:`, content.length);
        console.log(`${method.name} - 内容类型:`, response.headers.get('content-type'));
        console.log(`${method.name} - 前100字符:`, content.substring(0, 100));
        
        // 检查是否是M3U内容
        if (content.includes('#EXTM3U') || content.includes('#EXTINF')) {
          console.log(`${method.name} - 成功获取M3U内容！`);
          return { content, method: method.name };
        } else if (content.includes('<!DOCTYPE html>')) {
          console.log(`${method.name} - 返回了HTML页面，可能需要特殊处理`);
          continue;
        }
      }
    } catch (error) {
      console.log(`${method.name} - 失败:`, error.message);
    }
  }
  
  throw new Error('所有方法都无法获取M3U内容');
}

// 简化的M3U解析
function parseM3U(content) {
  const lines = content.split(/\r?\n/);
  const channels = [];
  let currentChannel = {};
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    if (line.startsWith('#EXTINF:')) {
      const match = line.match(/#EXTINF:\s*(-?\d+(?:\.\d+)?)(?:\s*,\s*(.*))?/);
      if (match) {
        let title = match[2] || '';
        
        // 提取group-title
        const groupMatch = line.match(/group-title="([^"]+)"/i);
        
        // 从标题中提取最后的部分
        if (title.includes(',')) {
          const parts = title.split(',');
          title = parts[parts.length - 1].trim();
        }
        
        currentChannel = {
          title: title || `频道${channels.length + 1}`,
          group: groupMatch ? groupMatch[1] : ''
        };
      }
    } else if (line && !line.startsWith('#') && currentChannel.title) {
      if (line.startsWith('http://') || line.startsWith('https://')) {
        currentChannel.url = line;
        channels.push({ ...currentChannel });
        currentChannel = {};
      }
    }
  }
  
  return channels.filter(ch => ch.title && ch.url && !ch.title.includes('INSERT_HERE'));
}

export default {
  async fetch(request) {
    const url = new URL(request.url);
    
    try {
      if (url.pathname === '/api/channels') {
        const result = await fetchM3UContent();
        const channels = parseM3U(result.content);
        
        return new Response(JSON.stringify({
          channels,
          method: result.method,
          count: channels.length
        }), {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        });
      }
      
      // 默认页面
      const result = await fetchM3UContent();
      const channels = parseM3U(result.content);
      
      const html = `
<!DOCTYPE html>
<html>
<head>
    <title>Live TV Player - 智能版</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        select { width: 100%; padding: 10px; margin: 10px 0; }
        button { padding: 10px 20px; margin: 5px; }
        video { width: 100%; max-width: 800px; margin-top: 20px; }
        .channel { margin: 5px 0; padding: 10px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>🔴 Live TV Player (智能版)</h1>
    
    <div class="info">
        <strong>获取方法:</strong> ${result.method}<br>
        <strong>频道数量:</strong> ${channels.length}<br>
        <strong>M3U源:</strong> ${M3U_URL}
    </div>
    
    <select id="channelSelect">
        <option value="">选择频道...</option>
        ${channels.map((ch, i) => 
          `<option value="${i}" data-url="${ch.url}">${ch.title} ${ch.group ? `(${ch.group})` : ''}</option>`
        ).join('')}
    </select>
    
    <button onclick="playChannel()">播放</button>
    <button onclick="location.reload()">刷新</button>
    
    <video id="player" controls style="display: none;"></video>
    
    <div id="channelList">
        <h3>所有频道列表:</h3>
        ${channels.slice(0, 20).map((ch, i) => 
          `<div class="channel">${i+1}. ${ch.title} (${ch.group || '无分组'})</div>`
        ).join('')}
        ${channels.length > 20 ? `<div>... 还有 ${channels.length - 20} 个频道</div>` : ''}
    </div>
    
    <script>
        function playChannel() {
            const select = document.getElementById('channelSelect');
            const player = document.getElementById('player');
            const option = select.options[select.selectedIndex];
            
            if (option && option.dataset.url) {
                player.src = option.dataset.url;
                player.style.display = 'block';
                player.play().catch(e => alert('播放失败: ' + e.message));
            } else {
                alert('请先选择频道');
            }
        }
    </script>
</body>
</html>`;
      
      return new Response(html, {
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      });
      
    } catch (error) {
      console.error('错误:', error);
      return new Response(`错误: ${error.message}`, {
        status: 500,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }
  }
};
