# Live TV Player - Go本地版

一个基于Go的本地直播电视播放器，完美解决Cloudflare Worker IP被屏蔽的问题。

## 🚀 快速开始

### 1. 安装Go
如果还没有安装Go，请访问 https://golang.org/dl/ 下载安装。

### 2. 运行服务
```bash
# 在项目目录下运行
go run main.go
```

### 3. 访问应用
打开浏览器访问：http://localhost:8080

## 📋 功能特性

- ✅ **本地运行** - 绕过所有IP限制
- ✅ **多M3U源** - 自动尝试多个直播源
- ✅ **智能解析** - 完整的M3U解析支持
- ✅ **分组显示** - 按频道分组组织
- ✅ **实时播放** - 支持HLS/M3U8流媒体
- ✅ **响应式界面** - 支持桌面和移动端
- ✅ **错误处理** - 完善的错误提示

## 🔧 技术优势

### vs Cloudflare Worker
- ❌ Worker: IP被屏蔽，无法访问某些M3U源
- ✅ Go本地: 使用你的本地IP，无限制

### vs VLC
- ❌ VLC: 只能本地播放，无法分享
- ✅ Go本地: 可以在局域网内分享（修改监听地址）

## 📡 API接口

### 获取频道列表
```
GET /api/channels
```

返回JSON格式的频道列表：
```json
{
  "channels": [
    {
      "id": "channel-0",
      "tvgId": "CCTV1",
      "name": "CCTV1 综合",
      "logo": "https://example.com/logo.png",
      "group": "央视频道",
      "url": "https://example.com/stream.m3u8"
    }
  ],
  "tvgUrl": "https://epg.example.com/epg.xml",
  "source": "https://example.com/playlist.m3u",
  "count": 100
}
```

## 🛠️ 自定义配置

### 修改端口
在 `main.go` 中修改：
```go
port := "8080"  // 改为你想要的端口
```

### 添加M3U源
在 `main.go` 中的 `m3uURLs` 数组中添加：
```go
var m3uURLs = []string{
    "https://your-m3u-source.com/playlist.m3u",
    // ... 其他源
}
```

### 局域网访问
修改监听地址：
```go
http.ListenAndServe("0.0.0.0:8080", nil)  // 允许局域网访问
```

## 🔍 故障排除

### 1. 端口被占用
```bash
# 查看端口占用
netstat -an | grep 8080

# 或者修改为其他端口
```

### 2. 无法播放某些频道
- 这是正常的，某些流可能已失效
- 尝试其他频道
- 检查网络连接

### 3. M3U源无法访问
- 程序会自动尝试多个源
- 查看控制台日志了解详情

## 📦 编译为可执行文件

### Windows
```bash
go build -o livetv.exe main.go
./livetv.exe
```

### Linux/macOS
```bash
go build -o livetv main.go
./livetv
```

### 交叉编译
```bash
# 编译为Windows版本
GOOS=windows GOARCH=amd64 go build -o livetv-windows.exe main.go

# 编译为Linux版本
GOOS=linux GOARCH=amd64 go build -o livetv-linux main.go

# 编译为macOS版本
GOOS=darwin GOARCH=amd64 go build -o livetv-macos main.go
```

## 🎯 使用场景

1. **个人使用** - 在本地观看直播电视
2. **家庭共享** - 在局域网内为多个设备提供服务
3. **开发测试** - 作为直播源的测试工具
4. **学习参考** - Go语言Web开发的实例

## 🔄 更新日志

- v1.0.0: 初始版本，支持M3U解析和播放
- 支持多M3U源自动切换
- 完整的Web界面
- RESTful API接口

## 📞 技术支持

如果遇到问题：
1. 检查Go版本（建议1.19+）
2. 查看控制台日志
3. 确认网络连接正常
4. 尝试不同的M3U源

## 🎉 享受观看！

现在你有了一个完全本地化的直播电视解决方案，不再受Cloudflare Worker的IP限制！
