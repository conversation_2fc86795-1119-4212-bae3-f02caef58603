// 简单版本 - 使用GitHub上的公开M3U源

// 使用GitHub上的公开M3U源（这些通常没有防盗链）
const M3U_URLS = [
  'https://raw.githubusercontent.com/fanmingming/live/main/tv/m3u/ipv6.m3u8',
  'https://raw.githubusercontent.com/YueChan/Live/main/IPTV.m3u8',
  'https://raw.githubusercontent.com/Kimentanm/aptv/master/m3u/iptv.m3u'
];

// 简单的M3U解析
function parseM3U(content) {
  const lines = content.split(/\r?\n/);
  const channels = [];
  let currentChannel = {};
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    if (line.startsWith('#EXTINF:')) {
      const match = line.match(/#EXTINF:\s*(-?\d+(?:\.\d+)?)(?:\s*,\s*(.*))?/);
      if (match) {
        let title = match[2] || '';
        
        // 提取group-title
        const groupMatch = line.match(/group-title="([^"]+)"/i);
        const logoMatch = line.match(/tvg-logo="([^"]+)"/i);
        
        // 从标题中提取最后的部分
        if (title.includes(',')) {
          const parts = title.split(',');
          title = parts[parts.length - 1].trim();
        }
        
        currentChannel = {
          title: title || `频道${channels.length + 1}`,
          group: groupMatch ? groupMatch[1] : '',
          logo: logoMatch ? logoMatch[1] : ''
        };
      }
    } else if (line && !line.startsWith('#') && currentChannel.title) {
      if (line.startsWith('http://') || line.startsWith('https://')) {
        currentChannel.url = line;
        channels.push({ ...currentChannel });
        currentChannel = {};
      }
    }
  }
  
  return channels.filter(ch => ch.title && ch.url);
}

// 尝试获取M3U内容
async function fetchM3UContent() {
  for (const url of M3U_URLS) {
    try {
      console.log(`尝试获取: ${url}`);
      const response = await fetch(url);
      
      if (response.ok) {
        const content = await response.text();
        console.log(`成功获取，长度: ${content.length}`);
        
        if (content.includes('#EXTM3U') || content.includes('#EXTINF')) {
          console.log(`✅ 有效的M3U内容: ${url}`);
          return { content, source: url };
        }
      }
    } catch (error) {
      console.log(`❌ 失败: ${url} - ${error.message}`);
    }
  }
  
  throw new Error('所有M3U源都无法访问');
}

export default {
  async fetch(request) {
    const url = new URL(request.url);
    
    try {
      if (url.pathname === '/api/channels') {
        const result = await fetchM3UContent();
        const channels = parseM3U(result.content);
        
        return new Response(JSON.stringify({
          channels,
          source: result.source,
          count: channels.length
        }), {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        });
      }
      
      // 默认页面
      const result = await fetchM3UContent();
      const channels = parseM3U(result.content);
      
      const channelOptions = channels.map((channel, index) => 
        `<option value="${index}" data-url="${channel.url}" data-logo="${channel.logo || ''}">${channel.title} ${channel.group ? `(${channel.group})` : ''}</option>`
      ).join('');

      const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live TV Player - 简单版</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .info { background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        select { width: 100%; padding: 12px; margin: 10px 0; border: 2px solid #ddd; border-radius: 8px; }
        button { padding: 12px 25px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 8px; cursor: pointer; }
        button:hover { background: #0056b3; }
        video { width: 100%; max-width: 800px; margin-top: 20px; border-radius: 8px; }
        .channel-info { margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; display: none; }
        .error { background: #ffe6e6; color: #d63031; padding: 15px; border-radius: 8px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔴 Live TV Player (简单版)</h1>
        
        <div class="info">
            <strong>✅ 成功加载!</strong><br>
            <strong>M3U源:</strong> ${result.source}<br>
            <strong>频道数量:</strong> ${channels.length}
        </div>
        
        <select id="channelSelect">
            <option value="">选择频道 (共${channels.length}个)...</option>
            ${channelOptions}
        </select>
        
        <div>
            <button onclick="playChannel()">▶️ 播放</button>
            <button onclick="location.reload()">🔄 刷新</button>
        </div>
        
        <div id="channelInfo" class="channel-info">
            <h3 id="channelTitle"></h3>
            <p id="channelGroup"></p>
        </div>
        
        <video id="player" controls style="display: none;"></video>
        
        <div id="error" class="error" style="display: none;"></div>
    </div>
    
    <script>
        function playChannel() {
            const select = document.getElementById('channelSelect');
            const player = document.getElementById('player');
            const channelInfo = document.getElementById('channelInfo');
            const channelTitle = document.getElementById('channelTitle');
            const channelGroup = document.getElementById('channelGroup');
            const error = document.getElementById('error');
            const option = select.options[select.selectedIndex];
            
            error.style.display = 'none';
            
            if (option && option.dataset.url) {
                // 显示频道信息
                channelTitle.textContent = option.textContent;
                channelGroup.textContent = '正在加载...';
                channelInfo.style.display = 'block';
                
                // 播放视频
                player.src = option.dataset.url;
                player.style.display = 'block';
                
                player.onloadstart = () => channelGroup.textContent = '正在连接...';
                player.oncanplay = () => channelGroup.textContent = '✅ 连接成功，开始播放';
                player.onerror = () => {
                    channelGroup.textContent = '❌ 播放失败';
                    error.textContent = '播放失败，请尝试其他频道';
                    error.style.display = 'block';
                };
                
                player.play().catch(e => {
                    console.error('播放错误:', e);
                    error.textContent = '播放失败: ' + e.message;
                    error.style.display = 'block';
                });
            } else {
                error.textContent = '请先选择一个频道';
                error.style.display = 'block';
            }
        }
        
        // 双击选择框直接播放
        document.getElementById('channelSelect').addEventListener('dblclick', playChannel);
    </script>
</body>
</html>`;
      
      return new Response(html, {
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      });
      
    } catch (error) {
      console.error('错误:', error);
      return new Response(`
<!DOCTYPE html>
<html><head><title>错误</title><meta charset="UTF-8"></head>
<body style="font-family: Arial; padding: 50px; text-align: center;">
    <h2>❌ 加载失败</h2>
    <p>错误: ${error.message}</p>
    <button onclick="location.reload()">重试</button>
</body></html>`, {
        status: 500,
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      });
    }
  }
};
