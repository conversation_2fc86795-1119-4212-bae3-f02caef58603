// 调试版本的Worker - 专门用来测试M3U解析

const M3U_URL = 'https://tv-1.iill.top/m3u/Gather';

// 简化的M3U解析函数
function parseM3U(content) {
  console.log('=== M3U解析开始 ===');
  console.log('内容长度:', content.length);
  console.log('前500字符:', content.substring(0, 500));
  
  const lines = content.split(/\r?\n/);
  console.log('总行数:', lines.length);
  
  const channels = [];
  let currentChannel = {};
  let extinfCount = 0;
  let urlCount = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    if (line.startsWith('#EXTINF:')) {
      extinfCount++;
      console.log(`第${extinfCount}个EXTINF (行${i+1}):`, line.substring(0, 100));
      
      // 简单的解析
      const match = line.match(/#EXTINF:\s*(-?\d+(?:\.\d+)?)(?:\s*,\s*(.*))?/);
      if (match) {
        const titlePart = match[2] || '';
        console.log('  标题部分:', titlePart);
        
        // 提取group-title
        const groupMatch = line.match(/group-title="([^"]+)"/i);
        const group = groupMatch ? groupMatch[1] : '';
        
        // 提取最后逗号后的内容作为标题
        let title = titlePart;
        if (titlePart.includes(',')) {
          const parts = titlePart.split(',');
          title = parts[parts.length - 1].trim();
        }
        
        currentChannel = {
          title: title || `频道${extinfCount}`,
          group: group,
          originalLine: line
        };
        
        console.log('  解析结果:', currentChannel.title, '分组:', currentChannel.group);
      }
    } else if (line && !line.startsWith('#') && currentChannel.title) {
      if (line.startsWith('http://') || line.startsWith('https://')) {
        urlCount++;
        currentChannel.url = line;
        channels.push({ ...currentChannel });
        console.log(`  添加频道${channels.length}:`, currentChannel.title, '-> URL:', line.substring(0, 50) + '...');
        currentChannel = {};
      }
    }
    
    // 只处理前50个频道用于调试
    if (channels.length >= 50) break;
  }
  
  console.log('=== 解析完成 ===');
  console.log('EXTINF行数:', extinfCount);
  console.log('URL行数:', urlCount);
  console.log('成功解析频道数:', channels.length);
  
  return channels;
}

export default {
  async fetch(request) {
    const url = new URL(request.url);
    
    if (url.pathname === '/debug') {
      // 调试页面
      try {
        console.log('开始获取M3U文件...');
        const response = await fetch(M3U_URL, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/plain, application/vnd.apple.mpegurl, application/x-mpegurl, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://tv-1.iill.top/',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });
        
        console.log('M3U响应状态:', response.status);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const m3uContent = await response.text();
        const channels = parseM3U(m3uContent);
        
        const debugHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>M3U解析调试</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: monospace; padding: 20px; }
        .channel { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .title { font-weight: bold; color: #333; }
        .group { color: #666; }
        .url { color: #007bff; word-break: break-all; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>M3U解析调试结果</h1>
    <p><strong>M3U源:</strong> ${M3U_URL}</p>
    <p><strong>内容长度:</strong> ${m3uContent.length} 字符</p>
    <p><strong>解析到的频道数:</strong> ${channels.length}</p>
    
    <h2>前10个频道:</h2>
    ${channels.slice(0, 10).map((channel, index) => `
        <div class="channel">
            <div class="title">${index + 1}. ${channel.title}</div>
            <div class="group">分组: ${channel.group || '无'}</div>
            <div class="url">URL: ${channel.url}</div>
        </div>
    `).join('')}
    
    <h2>原始M3U内容预览 (前2000字符):</h2>
    <pre>${m3uContent.substring(0, 2000).replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
    
    <h2>所有频道JSON:</h2>
    <pre>${JSON.stringify(channels, null, 2)}</pre>
</body>
</html>`;
        
        return new Response(debugHtml, {
          headers: { 'Content-Type': 'text/html; charset=utf-8' }
        });
        
      } catch (error) {
        console.error('调试错误:', error);
        return new Response(`调试错误: ${error.message}`, {
          status: 500,
          headers: { 'Content-Type': 'text/plain; charset=utf-8' }
        });
      }
    }
    
    // 默认返回简单说明
    return new Response(`
<!DOCTYPE html>
<html>
<head><title>M3U调试工具</title><meta charset="UTF-8"></head>
<body>
    <h1>M3U解析调试工具</h1>
    <p>访问 <a href="/debug">/debug</a> 查看详细的M3U解析过程</p>
</body>
</html>`, {
      headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
  }
};
