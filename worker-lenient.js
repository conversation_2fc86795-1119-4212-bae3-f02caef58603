// 宽松版本 - 减少过滤条件，显示更多频道

// 使用不会屏蔽Cloudflare IP的M3U源
const M3U_URLS = [
  'https://raw.githubusercontent.com/fanmingming/live/main/tv/m3u/ipv6.m3u8',
  'https://raw.githubusercontent.com/YueChan/Live/main/IPTV.m3u8',
  'https://raw.githubusercontent.com/Kimentanm/aptv/master/m3u/iptv.m3u',
  'https://raw.githubusercontent.com/joevess/IPTV/main/iptv.m3u8',
  'https://raw.githubusercontent.com/Fairy8o/IPTV/main/PDX-V4.m3u',
  'https://raw.githubusercontent.com/kimwang1978/collect-tv-txt/main/merged_output.m3u'
];

// 宽松的M3U解析函数
function parseM3U(m3uContent) {
  console.log('开始解析M3U，内容长度:', m3uContent.length);
  
  const channels = [];
  const lines = m3uContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);

  let tvgUrl = '';
  let channelIndex = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // 检查是否是 #EXTM3U 行，提取 tvg-url
    if (line.startsWith('#EXTM3U')) {
      const tvgUrlMatch = line.match(/(?:x-tvg-url|url-tvg)="([^"]*)"/);
      tvgUrl = tvgUrlMatch ? tvgUrlMatch[1].split(',')[0].trim() : '';
      continue;
    }

    // 检查是否是 #EXTINF 行
    if (line.startsWith('#EXTINF:')) {
      // 提取各种属性
      const tvgIdMatch = line.match(/tvg-id="([^"]*)"/);
      const tvgId = tvgIdMatch ? tvgIdMatch[1] : '';

      const tvgNameMatch = line.match(/tvg-name="([^"]*)"/);
      const tvgName = tvgNameMatch ? tvgNameMatch[1] : '';

      const tvgLogoMatch = line.match(/tvg-logo="([^"]*)"/);
      const logo = tvgLogoMatch ? tvgLogoMatch[1] : '';

      const groupTitleMatch = line.match(/group-title="([^"]*)"/);
      const group = groupTitleMatch ? groupTitleMatch[1] : '无分组';

      // 提取标题（#EXTINF 行最后的逗号后面的内容）
      const titleMatch = line.match(/,([^,]*)$/);
      const title = titleMatch ? titleMatch[1].trim() : '';

      // 优先使用标题，如果没有则使用 tvg-name
      const name = title || tvgName || tvgId || `频道${channelIndex + 1}`;

      // 检查下一行是否是URL
      if (i + 1 < lines.length && !lines[i + 1].startsWith('#')) {
        const url = lines[i + 1];

        // 只要有名称和URL就添加（更宽松的条件）
        if (name && url && (url.startsWith('http://') || url.startsWith('https://'))) {
          channels.push({
            id: `channel-${channelIndex}`,
            tvgId,
            title: name,
            name: tvgName,
            logo,
            group,
            url
          });
          
          if (channelIndex < 20) {
            console.log(`添加频道 ${channelIndex + 1}: "${name}" (${group}) - ${url.substring(0, 50)}...`);
          }
          channelIndex++;
        }

        // 跳过下一行，因为已经处理了
        i++;
      }
    }
  }

  console.log(`原始解析结果: ${channels.length}个频道`);
  
  // 更宽松的过滤条件 - 只过滤明显无效的
  const validChannels = channels.filter(channel => {
    // 基本验证
    if (!channel.title || !channel.url || channel.title.length === 0) {
      return false;
    }
    
    // 只过滤明显的无效内容
    const invalidKeywords = ['INSERT_HERE', 'MPD'];
    const hasInvalidKeyword = invalidKeywords.some(keyword => 
      channel.title.includes(keyword)
    );
    
    return !hasInvalidKeyword;
  });

  console.log(`过滤完成: 保留${validChannels.length}个频道`);
  
  // 按分组统计
  const groupStats = {};
  validChannels.forEach(channel => {
    const group = channel.group || '无分组';
    groupStats[group] = (groupStats[group] || 0) + 1;
  });
  
  console.log('分组统计:', groupStats);
  
  return {
    tvgUrl,
    channels: validChannels
  };
}

// 尝试获取M3U内容
async function fetchM3UContent() {
  const methods = [
    (url) => fetch(url),
    (url) => fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    })
  ];

  for (const url of M3U_URLS) {
    console.log(`尝试M3U源: ${url}`);
    
    for (let i = 0; i < methods.length; i++) {
      try {
        const response = await methods[i](url);
        
        if (response.ok) {
          const content = await response.text();
          
          if (content.includes('#EXTM3U') || content.includes('#EXTINF')) {
            console.log(`✅ 成功获取M3U内容！源: ${url}, 长度: ${content.length}`);
            return { content, source: url, method: i + 1 };
          }
        }
      } catch (error) {
        console.log(`方法${i + 1}: 失败 - ${error.message}`);
      }
    }
  }
  
  throw new Error('所有M3U源都无法访问');
}

export default {
  async fetch(request) {
    const url = new URL(request.url);
    
    try {
      if (url.pathname === '/api/channels') {
        const result = await fetchM3UContent();
        const parseResult = parseM3U(result.content);
        
        return new Response(JSON.stringify({
          channels: parseResult.channels,
          tvgUrl: parseResult.tvgUrl,
          source: result.source,
          method: result.method,
          count: parseResult.channels.length
        }), {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        });
      }
      
      // 默认页面
      const result = await fetchM3UContent();
      const parseResult = parseM3U(result.content);
      const channels = parseResult.channels;
      
      // 按分组组织频道
      const groupedChannels = {};
      channels.forEach(channel => {
        const group = channel.group || '无分组';
        if (!groupedChannels[group]) {
          groupedChannels[group] = [];
        }
        groupedChannels[group].push(channel);
      });
      
      // 生成分组选项
      let channelOptions = '<option value="">选择频道...</option>';
      Object.keys(groupedChannels).sort().forEach(group => {
        channelOptions += `<optgroup label="${group} (${groupedChannels[group].length}个)">`;
        groupedChannels[group].forEach((channel, index) => {
          const globalIndex = channels.indexOf(channel);
          channelOptions += `<option value="${globalIndex}" data-url="${channel.url}" data-logo="${channel.logo || ''}" data-group="${channel.group}">${channel.title}</option>`;
        });
        channelOptions += '</optgroup>';
      });

      const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live TV Player - 宽松版</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; margin: 0; }
        .container { max-width: 1200px; margin: 0 auto; background: rgba(255,255,255,0.95); padding: 30px; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }
        .info { background: linear-gradient(45deg, #e8f5e8, #f0f8f0); padding: 20px; border-radius: 10px; margin-bottom: 25px; border-left: 4px solid #28a745; }
        select { width: 100%; padding: 15px; margin: 15px 0; border: 2px solid #ddd; border-radius: 10px; font-size: 16px; background: white; }
        .controls { display: flex; gap: 15px; margin-bottom: 25px; flex-wrap: wrap; }
        button { padding: 15px 30px; background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; border-radius: 10px; cursor: pointer; font-size: 16px; font-weight: bold; transition: transform 0.2s; }
        button:hover { transform: translateY(-2px); }
        .channel-info { margin: 20px 0; padding: 20px; background: rgba(102, 126, 234, 0.1); border-radius: 10px; display: none; border-left: 4px solid #667eea; }
        video { width: 100%; margin-top: 20px; border-radius: 10px; }
        .error { background: #ffe6e6; color: #d63031; padding: 15px; border-radius: 8px; margin: 15px 0; display: none; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 20px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 1.8em; font-weight: bold; color: #667eea; }
        .debug { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔴 Live TV Player (宽松版)</h1>
        
        <div class="info">
            <strong>✅ 解析成功!</strong><br>
            <strong>M3U源:</strong> ${result.source}<br>
            <strong>频道总数:</strong> ${channels.length}<br>
            <strong>分组数:</strong> ${Object.keys(groupedChannels).length}
        </div>
        
        <div class="controls">
            <select id="channelSelect" style="flex: 1; min-width: 300px;">
                ${channelOptions}
            </select>
            <button onclick="playChannel()">▶️ 播放</button>
            <button onclick="location.reload()">🔄 刷新</button>
        </div>
        
        <div id="channelInfo" class="channel-info">
            <h3 id="channelTitle"></h3>
            <p id="channelStatus"></p>
        </div>
        
        <video id="player" controls style="display: none;"></video>
        <div id="error" class="error"></div>
        
        <div class="stats">
            ${Object.entries(groupedChannels).map(([group, channels]) => 
              `<div class="stat-card">
                <div class="stat-number">${channels.length}</div>
                <div class="stat-label">${group}</div>
              </div>`
            ).join('')}
        </div>
        
        <div class="debug">
            <strong>调试信息:</strong> 使用宽松过滤条件，显示更多频道。如果某些频道无法播放，这是正常的。
        </div>
    </div>
    
    <script>
        function showError(msg) {
            const error = document.getElementById('error');
            error.textContent = msg;
            error.style.display = 'block';
        }
        
        function playChannel() {
            const select = document.getElementById('channelSelect');
            const player = document.getElementById('player');
            const channelInfo = document.getElementById('channelInfo');
            const channelTitle = document.getElementById('channelTitle');
            const channelStatus = document.getElementById('channelStatus');
            const option = select.options[select.selectedIndex];
            
            document.getElementById('error').style.display = 'none';
            
            if (option && option.dataset.url) {
                channelTitle.textContent = option.textContent;
                channelStatus.textContent = '正在连接...';
                channelInfo.style.display = 'block';
                
                player.src = option.dataset.url;
                player.style.display = 'block';
                
                player.onloadstart = () => channelStatus.textContent = '⏳ 正在加载...';
                player.oncanplay = () => channelStatus.textContent = '✅ 连接成功';
                player.onerror = () => {
                    channelStatus.textContent = '❌ 播放失败';
                    showError('播放失败，请尝试其他频道');
                };
                
                player.play().catch(e => {
                    channelStatus.textContent = '❌ 播放失败';
                    showError('播放失败: ' + e.message);
                });
            } else {
                showError('请先选择一个频道');
            }
        }
        
        document.getElementById('channelSelect').addEventListener('dblclick', playChannel);
    </script>
</body>
</html>`;
      
      return new Response(html, {
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      });
      
    } catch (error) {
      console.error('错误:', error);
      return new Response(`错误: ${error.message}`, {
        status: 500,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }
  }
};
