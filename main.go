package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"regexp"
	"strings"
	"time"
)

// Channel 频道结构
type Channel struct {
	ID    string `json:"id"`
	TvgID string `json:"tvgId"`
	Name  string `json:"name"`
	Logo  string `json:"logo"`
	Group string `json:"group"`
	URL   string `json:"url"`
}

// M3UResult M3U解析结果
type M3UResult struct {
	TvgURL   string    `json:"tvgUrl"`
	Channels []Channel `json:"channels"`
}

// M3U源列表
var m3uURLs = []string{
	"https://tv-1.iill.top/m3u/Gather",
}

// 辅助函数：返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// 解析M3U文件
func parseM3U(content string) M3UResult {
	lines := strings.Split(content, "\n")
	var channels []Channel
	var tvgURL string
	channelIndex := 0

	// 正则表达式
	extm3uRegex := regexp.MustCompile(`(?:x-tvg-url|url-tvg)="([^"]*)"`)
	tvgIDRegex := regexp.MustCompile(`tvg-id="([^"]*)"`)
	tvgNameRegex := regexp.MustCompile(`tvg-name="([^"]*)"`)
	tvgLogoRegex := regexp.MustCompile(`tvg-logo="([^"]*)"`)
	groupTitleRegex := regexp.MustCompile(`group-title="([^"]*)"`)
	titleRegex := regexp.MustCompile(`,([^,]*)$`)

	for i := 0; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])

		// 检查 #EXTM3U 行
		if strings.HasPrefix(line, "#EXTM3U") {
			if match := extm3uRegex.FindStringSubmatch(line); len(match) > 1 {
				tvgURL = strings.Split(match[1], ",")[0]
			}
			continue
		}

		// 检查 #EXTINF 行
		if strings.HasPrefix(line, "#EXTINF:") {
			var tvgID, tvgName, logo, group, title string

			// 提取各种属性
			if match := tvgIDRegex.FindStringSubmatch(line); len(match) > 1 {
				tvgID = match[1]
			}
			if match := tvgNameRegex.FindStringSubmatch(line); len(match) > 1 {
				tvgName = match[1]
			}
			if match := tvgLogoRegex.FindStringSubmatch(line); len(match) > 1 {
				logo = match[1]
			}
			if match := groupTitleRegex.FindStringSubmatch(line); len(match) > 1 {
				group = match[1]
			} else {
				group = "无分组"
			}
			if match := titleRegex.FindStringSubmatch(line); len(match) > 1 {
				title = strings.TrimSpace(match[1])
			}

			// 优先使用标题，如果没有则使用 tvg-name
			name := title
			if name == "" {
				name = tvgName
			}
			if name == "" {
				name = tvgID
			}
			if name == "" {
				name = fmt.Sprintf("频道%d", channelIndex+1)
			}

			// 检查下一行是否是URL
			if i+1 < len(lines) && !strings.HasPrefix(strings.TrimSpace(lines[i+1]), "#") {
				url := strings.TrimSpace(lines[i+1])

				// 只有当有名称和URL时才添加
				if name != "" && url != "" && (strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://")) {
					// 过滤无效频道
					if !strings.Contains(name, "INSERT_HERE") &&
						!strings.Contains(name, "MPD") {

						channels = append(channels, Channel{
							ID:    fmt.Sprintf("channel-%d", channelIndex),
							TvgID: tvgID,
							Name:  name,
							Logo:  logo,
							Group: group,
							URL:   url,
						})
						channelIndex++
					}
				}

				// 跳过下一行
				i++
			}
		}
	}

	log.Printf("解析完成: 共%d个频道", len(channels))
	return M3UResult{
		TvgURL:   tvgURL,
		Channels: channels,
	}
}

// 获取M3U内容
func fetchM3UContent() (string, string, error) {
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 尝试多种不同的请求方法
	methods := []map[string]string{
		{
			"name":            "标准浏览器",
			"User-Agent":      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
			"Accept":          "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
			"Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
		},
		{
			"name":       "VLC播放器",
			"User-Agent": "VLC/3.0.16 LibVLC/3.0.16",
			"Accept":     "*/*",
		},
		{
			"name":       "简单请求",
			"User-Agent": "Go-http-client/1.1",
		},
	}

	for _, url := range m3uURLs {
		log.Printf("🔍 尝试获取: %s", url)

		for i, method := range methods {
			log.Printf("  方法%d: %s", i+1, method["name"])

			req, err := http.NewRequest("GET", url, nil)
			if err != nil {
				log.Printf("  ❌ 创建请求失败: %v", err)
				continue
			}

			// 设置请求头
			for key, value := range method {
				if key != "name" {
					req.Header.Set(key, value)
				}
			}

			resp, err := client.Do(req)
			if err != nil {
				log.Printf("  ❌ 请求失败: %v", err)
				continue
			}

			log.Printf("  📡 HTTP状态: %d %s", resp.StatusCode, resp.Status)

			if resp.StatusCode == http.StatusOK {
				body, err := io.ReadAll(resp.Body)
				resp.Body.Close()

				if err != nil {
					log.Printf("  ❌ 读取响应失败: %v", err)
					continue
				}

				content := string(body)
				log.Printf("  📄 响应内容长度: %d", len(content))
				log.Printf("  📄 响应内容预览 (前200字符): %s", content[:min(200, len(content))])

				if strings.Contains(content, "#EXTM3U") || strings.Contains(content, "#EXTINF") {
					log.Printf("  ✅ 成功获取M3U内容! (长度: %d)", len(content))
					return content, url, nil
				} else {
					log.Printf("  ⚠️  内容不是有效的M3U格式")
				}
			} else {
				body, _ := io.ReadAll(resp.Body)
				log.Printf("  ❌ HTTP错误: %d %s", resp.StatusCode, resp.Status)
				if len(body) > 0 {
					log.Printf("  ❌ 错误响应内容: %s", string(body)[:min(500, len(body))])
				}
			}
			resp.Body.Close()
		}
	}

	return "", "", fmt.Errorf("所有M3U源都无法访问")
}

// API处理器：获取频道列表
func channelsHandler(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("Content-Type", "application/json")

	if r.Method == "OPTIONS" {
		return
	}

	content, source, err := fetchM3UContent()
	if err != nil {
		http.Error(w, fmt.Sprintf(`{"error": "%s"}`, err.Error()), http.StatusInternalServerError)
		return
	}

	result := parseM3U(content)

	response := map[string]interface{}{
		"channels": result.Channels,
		"tvgUrl":   result.TvgURL,
		"source":   source,
		"count":    len(result.Channels),
	}

	json.NewEncoder(w).Encode(response)
}

// 主页处理器
func indexHandler(w http.ResponseWriter, r *http.Request) {
	content, source, err := fetchM3UContent()
	if err != nil {
		http.Error(w, fmt.Sprintf("获取M3U失败: %s", err.Error()), http.StatusInternalServerError)
		return
	}

	result := parseM3U(content)
	channels := result.Channels

	// 按分组组织频道
	groupedChannels := make(map[string][]Channel)
	for _, channel := range channels {
		group := channel.Group
		if group == "" {
			group = "无分组"
		}
		groupedChannels[group] = append(groupedChannels[group], channel)
	}

	// 生成频道选项HTML
	var channelOptions strings.Builder
	channelOptions.WriteString(`<option value="">选择频道...</option>`)

	for group, groupChannels := range groupedChannels {
		channelOptions.WriteString(fmt.Sprintf(`<optgroup label="%s (%d个)">`, group, len(groupChannels)))
		for _, channel := range groupChannels {
			// 找到全局索引
			globalIndex := -1
			for j, c := range channels {
				if c.ID == channel.ID {
					globalIndex = j
					break
				}
			}
			channelOptions.WriteString(fmt.Sprintf(
				`<option value="%d" data-url="%s" data-logo="%s" data-group="%s">%s</option>`,
				globalIndex, channel.URL, channel.Logo, channel.Group, channel.Name,
			))
		}
		channelOptions.WriteString(`</optgroup>`)
	}

	html := fmt.Sprintf(`
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live TV Player - Go版本</title>
    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; padding: 20px; background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%); min-height: 100vh; margin: 0; }
        .container { max-width: 1200px; margin: 0 auto; background: rgba(255,255,255,0.95); padding: 30px; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }
        .info { background: linear-gradient(45deg, #e8f5e8, #f0f8f0); padding: 20px; border-radius: 10px; margin-bottom: 25px; border-left: 4px solid #28a745; }
        select { width: 100%%; padding: 15px; margin: 15px 0; border: 2px solid #ddd; border-radius: 10px; font-size: 16px; background: white; }
        .controls { display: flex; gap: 15px; margin-bottom: 25px; flex-wrap: wrap; }
        button { padding: 15px 30px; background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; border-radius: 10px; cursor: pointer; font-size: 16px; font-weight: bold; transition: transform 0.2s; }
        button:hover { transform: translateY(-2px); }
        .channel-info { margin: 20px 0; padding: 20px; background: rgba(102, 126, 234, 0.1); border-radius: 10px; display: none; border-left: 4px solid #667eea; }
        .video-container { margin-top: 20px; border-radius: 10px; overflow: hidden; display: none; }
        .video-js { width: 100%%; height: 400px; }
        .error { background: #ffe6e6; color: #d63031; padding: 15px; border-radius: 8px; margin: 15px 0; display: none; }
        .format-info { background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔴 Live TV Player (Go本地版)</h1>
        
        <div class="info">
            <strong>✅ 本地服务运行中!</strong><br>
            <strong>M3U源:</strong> %s<br>
            <strong>频道总数:</strong> %d<br>
            <strong>分组数:</strong> %d<br>
            <strong>服务地址:</strong> http://localhost:8080
        </div>
        
        <div class="controls">
            <select id="channelSelect" style="flex: 1; min-width: 300px;">
                %s
            </select>
            <button onclick="playChannel()">▶️ 播放</button>
            <button onclick="location.reload()">🔄 刷新</button>
        </div>
        
        <div id="channelInfo" class="channel-info">
            <h3 id="channelTitle"></h3>
            <p id="channelStatus"></p>
            <div id="formatInfo" class="format-info"></div>
        </div>

        <div id="videoContainer" class="video-container">
            <video-js id="player" class="video-js vjs-default-skin" controls preload="auto" data-setup="{}">
                <p class="vjs-no-js">
                    要查看此视频，请启用 JavaScript，并考虑升级到
                    <a href="https://videojs.com/html5-video-support/" target="_blank">支持HTML5视频的浏览器</a>。
                </p>
            </video-js>
        </div>

        <div id="error" class="error"></div>
    </div>

    <!-- Video.js JavaScript -->
    <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>
    <script>
        let player = null;

        // 初始化Video.js播放器
        function initPlayer() {
            if (player) {
                player.dispose();
            }
            player = videojs('player', {
                fluid: true,
                responsive: true,
                playbackRates: [0.5, 1, 1.25, 1.5, 2],
                controls: true,
                preload: 'auto'
            });
        }

        // 页面加载完成后初始化播放器
        document.addEventListener('DOMContentLoaded', function() {
            initPlayer();
        });

        function showError(msg) {
            const error = document.getElementById('error');
            error.textContent = msg;
            error.style.display = 'block';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }

        function getVideoFormat(url) {
            if (url.includes('.flv')) return 'FLV';
            if (url.includes('.mp4')) return 'MP4';
            if (url.includes('.m3u8')) return 'HLS (M3U8)';
            if (url.includes('.ts')) return 'MPEG-TS';
            if (url.includes('/migu/')) return '咪咕直播流';
            if (url.includes('tv-1.iill.top')) return '直播流 (需解析)';
            return '直播流';
        }
        
        function playChannel() {
            const select = document.getElementById('channelSelect');
            const videoContainer = document.getElementById('videoContainer');
            const channelInfo = document.getElementById('channelInfo');
            const channelTitle = document.getElementById('channelTitle');
            const channelStatus = document.getElementById('channelStatus');
            const formatInfo = document.getElementById('formatInfo');
            const option = select.options[select.selectedIndex];

            hideError();

            if (option && option.dataset.url) {
                const originalUrl = option.dataset.url;
                const format = getVideoFormat(originalUrl);

                channelTitle.textContent = option.textContent;
                channelStatus.textContent = '正在连接...';
                formatInfo.textContent = '格式: ' + format + ' | 原始URL: ' + originalUrl;
                channelInfo.style.display = 'block';
                videoContainer.style.display = 'block';

                // 确保播放器已初始化
                if (!player) {
                    initPlayer();
                }

                // 尝试多种播放方式
                const playUrls = [
                    { url: '/proxy?url=' + encodeURIComponent(originalUrl), type: '代理播放' },
                    { url: originalUrl, type: '直接播放' }
                ];

                let currentUrlIndex = 0;

                function tryNextUrl() {
                    if (currentUrlIndex >= playUrls.length) {
                        channelStatus.textContent = '❌ 所有播放方式都失败';
                        showError('播放失败: 该频道可能暂时不可用或需要特殊播放器支持');
                        return;
                    }

                    const currentPlay = playUrls[currentUrlIndex];
                    channelStatus.textContent = '⏳ ' + currentPlay.type + '中...';
                    console.log('尝试播放:', currentPlay.type, currentPlay.url);

                    // 设置播放源
                    player.src({
                        src: currentPlay.url,
                        type: format.includes('MP4') ? 'video/mp4' :
                              format.includes('FLV') ? 'video/x-flv' :
                              format.includes('HLS') ? 'application/x-mpegURL' : 'video/mp4'
                    });

                    // 设置事件监听器
                    player.ready(() => {
                        player.on('loadstart', () => {
                            channelStatus.textContent = '⏳ 正在加载...';
                        });

                        player.on('canplay', () => {
                            channelStatus.textContent = '✅ 连接成功 (' + currentPlay.type + ')';
                            console.log('播放成功:', currentPlay.url);
                        });

                        player.on('error', (e) => {
                            console.error('播放错误:', e, currentPlay.url);
                            channelStatus.textContent = '❌ ' + currentPlay.type + '失败';
                            currentUrlIndex++;
                            setTimeout(tryNextUrl, 1000);
                        });

                        // 开始播放
                        player.play().catch(e => {
                            console.error('播放失败:', e);
                            currentUrlIndex++;
                            setTimeout(tryNextUrl, 1000);
                        });
                    });
                }

                // 开始尝试播放
                tryNextUrl();

            } else {
                showError('请先选择一个频道');
            }
        }
        
        document.getElementById('channelSelect').addEventListener('dblclick', playChannel);
    </script>
</body>
</html>`, source, len(channels), len(groupedChannels), channelOptions.String())

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

// 解析真实视频URL
func resolveVideoURL(originalURL string) (string, error) {
	client := &http.Client{
		Timeout: 10 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 允许最多10次重定向
			if len(via) >= 10 {
				return fmt.Errorf("too many redirects")
			}
			return nil
		},
	}

	// 先尝试GET请求获取内容
	req, err := http.NewRequest("GET", originalURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "VLC/3.0.16 LibVLC/3.0.16")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Referer", "https://tv-1.iill.top/")

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	contentType := resp.Header.Get("Content-Type")
	finalURL := resp.Request.URL.String()

	log.Printf("🔍 URL解析: %s -> %s (Content-Type: %s)", originalURL, finalURL, contentType)

	// 如果是文本内容，可能包含真实的视频URL
	if strings.Contains(contentType, "text/") {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return finalURL, nil
		}

		content := string(body)
		log.Printf("� 响应内容: %s", content[:min(200, len(content))])

		// 尝试从内容中提取URL
		lines := strings.Split(content, "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if strings.HasPrefix(line, "http://") || strings.HasPrefix(line, "https://") {
				log.Printf("✅ 找到真实视频URL: %s", line)
				return line, nil
			}
		}

		// 如果没找到URL，返回原始内容作为错误信息
		return "", fmt.Errorf("no video URL found in response: %s", content[:min(100, len(content))])
	}

	return finalURL, nil
}

// 视频代理处理器
func proxyHandler(w http.ResponseWriter, r *http.Request) {
	// 获取目标URL
	targetURL := r.URL.Query().Get("url")
	if targetURL == "" {
		http.Error(w, "Missing url parameter", http.StatusBadRequest)
		return
	}

	log.Printf("🎥 代理视频请求: %s", targetURL)

	// 尝试解析真实URL
	realURL, err := resolveVideoURL(targetURL)
	if err != nil {
		log.Printf("⚠️ URL解析失败，使用原始URL: %v", err)
		realURL = targetURL
	}

	// 创建代理请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	req, err := http.NewRequest("GET", realURL, nil)
	if err != nil {
		http.Error(w, "Invalid URL", http.StatusBadRequest)
		return
	}

	// 设置请求头
	req.Header.Set("User-Agent", "VLC/3.0.16 LibVLC/3.0.16")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	req.Header.Set("Referer", "https://tv-1.iill.top/")

	// 复制原始请求的一些头部
	for name, values := range r.Header {
		if name == "Range" || name == "If-Range" || name == "If-Modified-Since" {
			for _, value := range values {
				req.Header.Add(name, value)
			}
		}
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("❌ 代理请求失败: %v", err)
		http.Error(w, "Proxy request failed", http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()

	log.Printf("✅ 代理响应: %d %s (Content-Type: %s)", resp.StatusCode, resp.Status, resp.Header.Get("Content-Type"))

	// 设置响应头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, HEAD, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Range, Content-Type")
	w.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Range, Accept-Ranges")

	// 复制响应头
	for name, values := range resp.Header {
		for _, value := range values {
			w.Header().Add(name, value)
		}
	}

	// 设置状态码
	w.WriteHeader(resp.StatusCode)

	// 复制响应体
	io.Copy(w, resp.Body)
}

// URL解析测试处理器
func resolveHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Content-Type", "application/json")

	targetURL := r.URL.Query().Get("url")
	if targetURL == "" {
		http.Error(w, `{"error": "Missing url parameter"}`, http.StatusBadRequest)
		return
	}

	realURL, err := resolveVideoURL(targetURL)
	response := map[string]interface{}{
		"original": targetURL,
		"resolved": realURL,
		"success":  err == nil,
	}

	if err != nil {
		response["error"] = err.Error()
	}

	json.NewEncoder(w).Encode(response)
}

func main() {
	// 路由设置
	http.HandleFunc("/", indexHandler)
	http.HandleFunc("/api/channels", channelsHandler)
	http.HandleFunc("/api/resolve", resolveHandler)
	http.HandleFunc("/proxy", proxyHandler)

	// 启动服务器
	port := "8081"
	log.Printf("🚀 Live TV Player 服务启动")
	log.Printf("📺 访问地址: http://localhost:%s", port)
	log.Printf("🔧 API地址: http://localhost:%s/api/channels", port)
	log.Printf("🎥 视频代理: http://localhost:%s/proxy?url=<video_url>", port)

	if err := http.ListenAndServe(":"+port, nil); err != nil {
		log.Fatal("服务器启动失败:", err)
	}
}
