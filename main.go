package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"regexp"
	"strings"
	"time"
)

// Channel 频道结构
type Channel struct {
	ID    string `json:"id"`
	TvgID string `json:"tvgId"`
	Name  string `json:"name"`
	Logo  string `json:"logo"`
	Group string `json:"group"`
	URL   string `json:"url"`
}

// M3UResult M3U解析结果
type M3UResult struct {
	TvgURL   string    `json:"tvgUrl"`
	Channels []Channel `json:"channels"`
}

// M3U源列表
var m3uURLs = []string{
	"https://tv-1.iill.top/m3u/Gather",
}

// 辅助函数：返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// 解析M3U文件
func parseM3U(content string) M3UResult {
	lines := strings.Split(content, "\n")
	var channels []Channel
	var tvgURL string
	channelIndex := 0

	// 正则表达式
	extm3uRegex := regexp.MustCompile(`(?:x-tvg-url|url-tvg)="([^"]*)"`)
	tvgIDRegex := regexp.MustCompile(`tvg-id="([^"]*)"`)
	tvgNameRegex := regexp.MustCompile(`tvg-name="([^"]*)"`)
	tvgLogoRegex := regexp.MustCompile(`tvg-logo="([^"]*)"`)
	groupTitleRegex := regexp.MustCompile(`group-title="([^"]*)"`)
	titleRegex := regexp.MustCompile(`,([^,]*)$`)

	for i := 0; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])

		// 检查 #EXTM3U 行
		if strings.HasPrefix(line, "#EXTM3U") {
			if match := extm3uRegex.FindStringSubmatch(line); len(match) > 1 {
				tvgURL = strings.Split(match[1], ",")[0]
			}
			continue
		}

		// 检查 #EXTINF 行
		if strings.HasPrefix(line, "#EXTINF:") {
			var tvgID, tvgName, logo, group, title string

			// 提取各种属性
			if match := tvgIDRegex.FindStringSubmatch(line); len(match) > 1 {
				tvgID = match[1]
			}
			if match := tvgNameRegex.FindStringSubmatch(line); len(match) > 1 {
				tvgName = match[1]
			}
			if match := tvgLogoRegex.FindStringSubmatch(line); len(match) > 1 {
				logo = match[1]
			}
			if match := groupTitleRegex.FindStringSubmatch(line); len(match) > 1 {
				group = match[1]
			} else {
				group = "无分组"
			}
			if match := titleRegex.FindStringSubmatch(line); len(match) > 1 {
				title = strings.TrimSpace(match[1])
			}

			// 优先使用标题，如果没有则使用 tvg-name
			name := title
			if name == "" {
				name = tvgName
			}
			if name == "" {
				name = tvgID
			}
			if name == "" {
				name = fmt.Sprintf("频道%d", channelIndex+1)
			}

			// 检查下一行是否是URL
			if i+1 < len(lines) && !strings.HasPrefix(strings.TrimSpace(lines[i+1]), "#") {
				url := strings.TrimSpace(lines[i+1])

				// 只有当有名称和URL时才添加
				if name != "" && url != "" && (strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://")) {
					// 过滤无效频道
					if !strings.Contains(name, "INSERT_HERE") &&
						!strings.Contains(name, "MPD") {

						channels = append(channels, Channel{
							ID:    fmt.Sprintf("channel-%d", channelIndex),
							TvgID: tvgID,
							Name:  name,
							Logo:  logo,
							Group: group,
							URL:   url,
						})
						channelIndex++
					}
				}

				// 跳过下一行
				i++
			}
		}
	}

	log.Printf("解析完成: 共%d个频道", len(channels))
	return M3UResult{
		TvgURL:   tvgURL,
		Channels: channels,
	}
}

// 获取M3U内容
func fetchM3UContent() (string, string, error) {
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 尝试多种不同的请求方法
	methods := []map[string]string{
		{
			"name":            "标准浏览器",
			"User-Agent":      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
			"Accept":          "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
			"Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
		},
		{
			"name":       "VLC播放器",
			"User-Agent": "VLC/3.0.16 LibVLC/3.0.16",
			"Accept":     "*/*",
		},
		{
			"name":       "简单请求",
			"User-Agent": "Go-http-client/1.1",
		},
	}

	for _, url := range m3uURLs {
		log.Printf("🔍 尝试获取: %s", url)

		for i, method := range methods {
			log.Printf("  方法%d: %s", i+1, method["name"])

			req, err := http.NewRequest("GET", url, nil)
			if err != nil {
				log.Printf("  ❌ 创建请求失败: %v", err)
				continue
			}

			// 设置请求头
			for key, value := range method {
				if key != "name" {
					req.Header.Set(key, value)
				}
			}

			resp, err := client.Do(req)
			if err != nil {
				log.Printf("  ❌ 请求失败: %v", err)
				continue
			}

			log.Printf("  📡 HTTP状态: %d %s", resp.StatusCode, resp.Status)

			if resp.StatusCode == http.StatusOK {
				body, err := io.ReadAll(resp.Body)
				resp.Body.Close()

				if err != nil {
					log.Printf("  ❌ 读取响应失败: %v", err)
					continue
				}

				content := string(body)
				log.Printf("  📄 响应内容长度: %d", len(content))
				log.Printf("  📄 响应内容预览 (前200字符): %s", content[:min(200, len(content))])

				if strings.Contains(content, "#EXTM3U") || strings.Contains(content, "#EXTINF") {
					log.Printf("  ✅ 成功获取M3U内容! (长度: %d)", len(content))
					return content, url, nil
				} else {
					log.Printf("  ⚠️  内容不是有效的M3U格式")
				}
			} else {
				body, _ := io.ReadAll(resp.Body)
				log.Printf("  ❌ HTTP错误: %d %s", resp.StatusCode, resp.Status)
				if len(body) > 0 {
					log.Printf("  ❌ 错误响应内容: %s", string(body)[:min(500, len(body))])
				}
			}
			resp.Body.Close()
		}
	}

	return "", "", fmt.Errorf("所有M3U源都无法访问")
}

// API处理器：获取频道列表
func channelsHandler(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("Content-Type", "application/json")

	if r.Method == "OPTIONS" {
		return
	}

	content, source, err := fetchM3UContent()
	if err != nil {
		http.Error(w, fmt.Sprintf(`{"error": "%s"}`, err.Error()), http.StatusInternalServerError)
		return
	}

	result := parseM3U(content)

	response := map[string]interface{}{
		"channels": result.Channels,
		"tvgUrl":   result.TvgURL,
		"source":   source,
		"count":    len(result.Channels),
	}

	json.NewEncoder(w).Encode(response)
}

// 主页处理器
func indexHandler(w http.ResponseWriter, r *http.Request) {
	content, source, err := fetchM3UContent()
	if err != nil {
		http.Error(w, fmt.Sprintf("获取M3U失败: %s", err.Error()), http.StatusInternalServerError)
		return
	}

	result := parseM3U(content)
	channels := result.Channels

	// 按分组组织频道
	groupedChannels := make(map[string][]Channel)
	for _, channel := range channels {
		group := channel.Group
		if group == "" {
			group = "无分组"
		}
		groupedChannels[group] = append(groupedChannels[group], channel)
	}

	// 生成频道选项HTML
	var channelOptions strings.Builder
	channelOptions.WriteString(`<option value="">选择频道...</option>`)

	for group, groupChannels := range groupedChannels {
		channelOptions.WriteString(fmt.Sprintf(`<optgroup label="%s (%d个)">`, group, len(groupChannels)))
		for _, channel := range groupChannels {
			// 找到全局索引
			globalIndex := -1
			for j, c := range channels {
				if c.ID == channel.ID {
					globalIndex = j
					break
				}
			}
			channelOptions.WriteString(fmt.Sprintf(
				`<option value="%d" data-url="%s" data-logo="%s" data-group="%s">%s</option>`,
				globalIndex, channel.URL, channel.Logo, channel.Group, channel.Name,
			))
		}
		channelOptions.WriteString(`</optgroup>`)
	}

	html := fmt.Sprintf(`
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live TV Player - Go版本</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; padding: 20px; background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%); min-height: 100vh; margin: 0; }
        .container { max-width: 1200px; margin: 0 auto; background: rgba(255,255,255,0.95); padding: 30px; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }
        .info { background: linear-gradient(45deg, #e8f5e8, #f0f8f0); padding: 20px; border-radius: 10px; margin-bottom: 25px; border-left: 4px solid #28a745; }
        select { width: 100%%; padding: 15px; margin: 15px 0; border: 2px solid #ddd; border-radius: 10px; font-size: 16px; background: white; }
        .controls { display: flex; gap: 15px; margin-bottom: 25px; flex-wrap: wrap; }
        button { padding: 15px 30px; background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; border-radius: 10px; cursor: pointer; font-size: 16px; font-weight: bold; transition: transform 0.2s; }
        button:hover { transform: translateY(-2px); }
        .channel-info { margin: 20px 0; padding: 20px; background: rgba(102, 126, 234, 0.1); border-radius: 10px; display: none; border-left: 4px solid #667eea; }
        video { width: 100%%; margin-top: 20px; border-radius: 10px; }
        .error { background: #ffe6e6; color: #d63031; padding: 15px; border-radius: 8px; margin: 15px 0; display: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔴 Live TV Player (Go本地版)</h1>
        
        <div class="info">
            <strong>✅ 本地服务运行中!</strong><br>
            <strong>M3U源:</strong> %s<br>
            <strong>频道总数:</strong> %d<br>
            <strong>分组数:</strong> %d<br>
            <strong>服务地址:</strong> http://localhost:8080
        </div>
        
        <div class="controls">
            <select id="channelSelect" style="flex: 1; min-width: 300px;">
                %s
            </select>
            <button onclick="playChannel()">▶️ 播放</button>
            <button onclick="location.reload()">🔄 刷新</button>
        </div>
        
        <div id="channelInfo" class="channel-info">
            <h3 id="channelTitle"></h3>
            <p id="channelStatus"></p>
        </div>
        
        <video id="player" controls style="display: none;"></video>
        <div id="error" class="error"></div>
    </div>
    
    <script>
        function showError(msg) {
            const error = document.getElementById('error');
            error.textContent = msg;
            error.style.display = 'block';
        }
        
        function playChannel() {
            const select = document.getElementById('channelSelect');
            const player = document.getElementById('player');
            const channelInfo = document.getElementById('channelInfo');
            const channelTitle = document.getElementById('channelTitle');
            const channelStatus = document.getElementById('channelStatus');
            const option = select.options[select.selectedIndex];
            
            document.getElementById('error').style.display = 'none';
            
            if (option && option.dataset.url) {
                channelTitle.textContent = option.textContent;
                channelStatus.textContent = '正在连接...';
                channelInfo.style.display = 'block';
                
                player.src = option.dataset.url;
                player.style.display = 'block';
                
                player.onloadstart = () => channelStatus.textContent = '⏳ 正在加载...';
                player.oncanplay = () => channelStatus.textContent = '✅ 连接成功';
                player.onerror = () => {
                    channelStatus.textContent = '❌ 播放失败';
                    showError('播放失败，请尝试其他频道');
                };
                
                player.play().catch(e => {
                    channelStatus.textContent = '❌ 播放失败';
                    showError('播放失败: ' + e.message);
                });
            } else {
                showError('请先选择一个频道');
            }
        }
        
        document.getElementById('channelSelect').addEventListener('dblclick', playChannel);
    </script>
</body>
</html>`, source, len(channels), len(groupedChannels), channelOptions.String())

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

func main() {
	// 路由设置
	http.HandleFunc("/", indexHandler)
	http.HandleFunc("/api/channels", channelsHandler)

	// 启动服务器
	port := "8080"
	log.Printf("🚀 Live TV Player 服务启动")
	log.Printf("📺 访问地址: http://localhost:%s", port)
	log.Printf("🔧 API地址: http://localhost:%s/api/channels", port)

	if err := http.ListenAndServe(":"+port, nil); err != nil {
		log.Fatal("服务器启动失败:", err)
	}
}
