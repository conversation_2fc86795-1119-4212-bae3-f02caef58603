{"name": "livetv-player", "version": "1.0.0", "description": "A Cloudflare Worker-based live TV player for M3U playlists", "main": "worker.js", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:dev": "wrangler deploy --env dev", "deploy:prod": "wrangler deploy --env production", "tail": "wrangler tail", "test": "wrangler dev --local"}, "keywords": ["cloudflare", "worker", "live-tv", "m3u", "streaming", "hls"], "author": "Your Name", "license": "MIT", "devDependencies": {"wrangler": "^3.0.0"}, "engines": {"node": ">=16.0.0"}}